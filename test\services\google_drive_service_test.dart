import 'package:flutter_test/flutter_test.dart';
import 'package:vitabrosse_pro/services/google_drive_service.dart';
import 'package:vitabrosse_pro/models/upload_result.dart';
import 'dart:io';

void main() {
  group('GoogleDriveService', () {
    test('should initialize without throwing errors', () async {
      // This test verifies that the service can initialize
      // Even if folder access fails, it should not throw
      expect(() async => await GoogleDriveService.initialize(), 
             returnsNormally);
    });

    test('should extract file ID from Google Drive URL correctly', () {
      const testUrl = 'https://drive.google.com/file/d/test123abc/view';
      final fileId = GoogleDriveService.extractFileId(testUrl);
      
      expect(fileId, equals('test123abc'));
    });

    test('should return null for invalid Google Drive URL', () {
      const invalidUrl = 'https://example.com/invalid-url';
      final fileId = GoogleDriveService.extractFileId(invalidUrl);
      
      expect(fileId, isNull);
    });

    test('should clean photo URLs correctly', () {
      final photos = [
        'https://drive.google.com/file/d/test123/view',
        '/local/path/image.jpg',
        'https://drive.google.com/file/d/test456/view',
        'file:///another/local/path.png',
      ];
      
      final cleanedPhotos = GoogleDriveService.cleanPhotoUrls(photos);
      
      expect(cleanedPhotos, hasLength(2));
      expect(cleanedPhotos, contains('https://drive.google.com/file/d/test123/view'));
      expect(cleanedPhotos, contains('https://drive.google.com/file/d/test456/view'));
    });

    test('should get public URL correctly', () {
      const fileId = 'test123abc';
      final publicUrl = GoogleDriveService.getPublicUrl(fileId);
      
      expect(publicUrl, equals('https://drive.google.com/file/d/test123abc/view'));
    });

    test('should get VitaBrosse folder URL correctly', () {
      final folderUrl = GoogleDriveService.getVitaBrosseFolderUrl();
      
      expect(folderUrl, contains('https://drive.google.com/drive/folders/'));
      expect(folderUrl, contains('1ryAprErbjJ1l-PZSKlOPcMlPxzVZyb9c'));
    });
  });
}