import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'firebase_options.dart';
import 'services/firebase_service.dart';
import 'providers/firebase_client_provider.dart';
import 'providers/produit_provider.dart';
import 'providers/commande_provider.dart';
import 'providers/merchandiser_provider.dart';
import 'providers/parcours_provider.dart';
import 'providers/catalogue_provider.dart';
import 'providers/tache_merchandising_provider.dart';
import 'providers/mission_provider.dart';
import 'providers/rapport_provider.dart';
import 'providers/devis_provider.dart';
import 'screens/auth/login_screen.dart';
import 'providers/auth_provider.dart';
import 'providers/firebase_auth_provider.dart';
import 'widgets/vitabrosse_logo.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Launch app immediately - no blocking initialization
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VitaBrosse Pro',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.green,
        useMaterial3: true,
        primaryColor: const Color(0xFF10B981),
        scaffoldBackgroundColor: Colors.white, // Prevent white flash
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF10B981),
          brightness: Brightness.light,
          surface: Colors.white, // Set surface to white
        ),
      ),
      home: const InitializationWrapper(),
    );
  }
}

class InitializationWrapper extends StatefulWidget {
  const InitializationWrapper({super.key});

  @override
  State<InitializationWrapper> createState() => _InitializationWrapperState();
}

class _InitializationWrapperState extends State<InitializationWrapper> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Initialize Firebase and Firestore in background
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // Initialize Firestore (allow it to fail gracefully)
      try {
        await FirebaseService.initializeFirestore();
      } catch (e) {
        print('Firestore init warning: $e');
        // Continue even if Firestore init fails
      }

      // Set initialized flag and navigate
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });

        // Small delay to show the branding
        await Future.delayed(const Duration(milliseconds: 500));

        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const MainApp()),
          );
        }
      }
    } catch (e) {
      print('Erreur lors de l\'initialisation Firebase: $e');
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => MyErrorApp(error: e.toString()),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white, // Ensure white background
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // VitaBrosse Logo - Same as login/signup screens
            const VitaBrosseLogo(height: 80, showText: false),
            const SizedBox(height: 20),

            // App subtitle
            const Text(
              'Gestion Commerciale',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF6B7280),
              ),
            ),
            const SizedBox(height: 40),

            // Loading indicator (only show if not initialized)
            if (!_isInitialized)
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF10B981)),
                  strokeWidth: 2,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Core providers - loaded immediately
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        ChangeNotifierProvider(create: (context) => FirebaseAuthProvider()),

        // Business providers - created lazily
        ChangeNotifierProvider(create: (context) => FirebaseClientProvider()),
        ChangeNotifierProvider(create: (context) => ProduitProvider()),
        ChangeNotifierProvider(create: (context) => CommandeProvider()),
        ChangeNotifierProvider(create: (context) => DevisProvider()),
        ChangeNotifierProvider(create: (context) => MerchandiserProvider()),
        ChangeNotifierProvider(create: (context) => ParcoursProvider()),
        ChangeNotifierProvider(create: (context) => CatalogueProvider()),
        ChangeNotifierProvider(
          create: (context) => TacheMerchandisingProvider(),
        ),
        ChangeNotifierProvider(create: (context) => MissionProvider()),
        ChangeNotifierProvider(create: (context) => RapportProvider()),
      ],
      child: MaterialApp(
        title: 'VitaBrosse Pro',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
          primaryColor: const Color(0xFF10B981),
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF10B981),
            brightness: Brightness.light,
          ),
        ),
        home: const LoginScreen(),
      ),
    );
  }
}

class MyErrorApp extends StatelessWidget {
  final String error;

  const MyErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VitaBrosse Pro - Erreur',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(primarySwatch: Colors.red, useMaterial3: true),
      home: Scaffold(
        appBar: AppBar(
          title: Text('Erreur d\'initialisation'),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 100, color: Colors.red),
                SizedBox(height: 20),
                Text(
                  'VitaBrosse Pro',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 20),
                Text(
                  'Erreur lors de l\'initialisation Firebase:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 10),
                Text(
                  error,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: Colors.red),
                ),
                SizedBox(height: 30),
                ElevatedButton(
                  onPressed: () {
                    // Relancer l'application
                    main();
                  },
                  child: Text('Réessayer'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
