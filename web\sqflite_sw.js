importScripts('https://cdn.jsdelivr.net/npm/sql.js@1.6.2/dist/sql-wasm.js');

// Configuration pour SQLite en mode web
const SQL_WASM_PATH = 'https://cdn.jsdelivr.net/npm/sql.js@1.6.2/dist/sql-wasm.wasm';

self.addEventListener('install', (event) => {
  console.log('Service worker installé');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('Service worker activé');
  event.waitUntil(self.clients.claim());
});

self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Initialisation de SQL.js
let SQL;
initSqlJs({
  locateFile: file => SQL_WASM_PATH
}).then(sql => {
  SQL = sql;
  console.log('SQL.js initialisé');
});
