enum TypeTache {
  visite,
  action,
  formation,
  inventaire,
  promotion,
  maintenance,
  autre,
}

enum StatutTache { planifiee, en_cours, terminee, reportee, annulee }

enum PrioriteTache { basse, normale, haute, urgente }

class TacheMerchandising {
  final int? id;
  final String titre;
  final String description;
  final TypeTache type;
  final StatutTache statut;
  final PrioriteTache priorite;
  final int merchandiserId;
  final String? clientId;
  final DateTime dateEcheance;
  final DateTime? heureDebut;
  final DateTime? heureFin;
  final Duration? dureeEstimee;
  final String? commentaires;
  final List<String>? photos;
  final Map<String, dynamic>? donnees;
  final DateTime dateCreation;
  final DateTime? dateRealisation;
  final bool rappel;
  final DateTime? dateRappel;

  TacheMerchandising({
    this.id,
    required this.titre,
    required this.description,
    required this.type,
    this.statut = StatutTache.planifiee,
    this.priorite = PrioriteTache.normale,
    required this.merchandiserId,
    this.clientId,
    required this.dateEcheance,
    this.heureDebut,
    this.heureFin,
    this.dureeEstimee,
    this.commentaires,
    this.photos,
    this.donnees,
    required this.dateCreation,
    this.dateRealisation,
    this.rappel = false,
    this.dateRappel,
  });

  // Convertir une TacheMerchandising en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'titre': titre,
      'description': description,
      'type': type.name,
      'statut': statut.name,
      'priorite': priorite.name,
      'merchandiserId': merchandiserId,
      'clientId': clientId,
      'dateEcheance': dateEcheance.toIso8601String(),
      'heureDebut': heureDebut?.toIso8601String(),
      'heureFin': heureFin?.toIso8601String(),
      'dureeEstimee': dureeEstimee?.inMinutes,
      'commentaires': commentaires,
      'photos': photos?.join(','),
      'donnees':
          donnees != null
              ? donnees!.entries.map((e) => '${e.key}:${e.value}').join(';')
              : null,
      'dateCreation': dateCreation.toIso8601String(),
      'dateRealisation': dateRealisation?.toIso8601String(),
      'rappel': rappel ? 1 : 0,
      'dateRappel': dateRappel?.toIso8601String(),
    };
  }

  // Créer une TacheMerchandising à partir d'une Map de la base de données
  factory TacheMerchandising.fromMap(Map<String, dynamic> map) {
    Map<String, dynamic>? donnees;
    if (map['donnees'] != null && map['donnees'].isNotEmpty) {
      donnees = {};
      final pairs = map['donnees'].split(';');
      for (String pair in pairs) {
        final keyValue = pair.split(':');
        if (keyValue.length == 2) {
          donnees[keyValue[0]] = keyValue[1];
        }
      }
    }

    return TacheMerchandising(
      id: map['id'],
      titre: map['titre'],
      description: map['description'],
      type: TypeTache.values.firstWhere((t) => t.name == map['type']),
      statut: StatutTache.values.firstWhere((s) => s.name == map['statut']),
      priorite: PrioriteTache.values.firstWhere(
        (p) => p.name == map['priorite'],
      ),
      merchandiserId: map['merchandiserId'],
      clientId: map['clientId'],
      dateEcheance: DateTime.parse(map['dateEcheance']),
      heureDebut:
          map['heureDebut'] != null ? DateTime.parse(map['heureDebut']) : null,
      heureFin:
          map['heureFin'] != null ? DateTime.parse(map['heureFin']) : null,
      dureeEstimee:
          map['dureeEstimee'] != null
              ? Duration(minutes: map['dureeEstimee'])
              : null,
      commentaires: map['commentaires'],
      photos:
          map['photos'] != null && map['photos'].isNotEmpty
              ? map['photos'].split(',')
              : null,
      donnees: donnees,
      dateCreation: DateTime.parse(map['dateCreation']),
      dateRealisation:
          map['dateRealisation'] != null
              ? DateTime.parse(map['dateRealisation'])
              : null,
      rappel: map['rappel'] == 1,
      dateRappel:
          map['dateRappel'] != null ? DateTime.parse(map['dateRappel']) : null,
    );
  }

  // Créer une copie de la tâche avec des modifications
  TacheMerchandising copyWith({
    int? id,
    String? titre,
    String? description,
    TypeTache? type,
    StatutTache? statut,
    PrioriteTache? priorite,
    int? merchandiserId,
    String? clientId,
    DateTime? dateEcheance,
    DateTime? heureDebut,
    DateTime? heureFin,
    Duration? dureeEstimee,
    String? commentaires,
    List<String>? photos,
    Map<String, dynamic>? donnees,
    DateTime? dateCreation,
    DateTime? dateRealisation,
    bool? rappel,
    DateTime? dateRappel,
  }) {
    return TacheMerchandising(
      id: id ?? this.id,
      titre: titre ?? this.titre,
      description: description ?? this.description,
      type: type ?? this.type,
      statut: statut ?? this.statut,
      priorite: priorite ?? this.priorite,
      merchandiserId: merchandiserId ?? this.merchandiserId,
      clientId: clientId ?? this.clientId,
      dateEcheance: dateEcheance ?? this.dateEcheance,
      heureDebut: heureDebut ?? this.heureDebut,
      heureFin: heureFin ?? this.heureFin,
      dureeEstimee: dureeEstimee ?? this.dureeEstimee,
      commentaires: commentaires ?? this.commentaires,
      photos: photos ?? this.photos,
      donnees: donnees ?? this.donnees,
      dateCreation: dateCreation ?? this.dateCreation,
      dateRealisation: dateRealisation ?? this.dateRealisation,
      rappel: rappel ?? this.rappel,
      dateRappel: dateRappel ?? this.dateRappel,
    );
  }

  // Calculer la durée réelle de la tâche
  Duration? get dureeReelle {
    if (heureDebut != null && heureFin != null) {
      return heureFin!.difference(heureDebut!);
    }
    return null;
  }

  // Vérifier si la tâche est en retard
  bool get estEnRetard {
    if (statut == StatutTache.terminee) return false;
    return DateTime.now().isAfter(dateEcheance);
  }

  // Vérifier si la tâche est prévue pour aujourd'hui
  bool get estAujourdhui {
    final aujourdhui = DateTime.now();
    return dateEcheance.year == aujourdhui.year &&
        dateEcheance.month == aujourdhui.month &&
        dateEcheance.day == aujourdhui.day;
  }

  // Obtenir la couleur selon la priorité
  String get couleurPriorite {
    switch (priorite) {
      case PrioriteTache.basse:
        return '#10B981'; // Vert
      case PrioriteTache.normale:
        return '#3B82F6'; // Bleu
      case PrioriteTache.haute:
        return '#F59E0B'; // Orange
      case PrioriteTache.urgente:
        return '#EF4444'; // Rouge
    }
  }

  // Obtenir l'icône selon le type
  String get iconeType {
    switch (type) {
      case TypeTache.visite:
        return 'location_on';
      case TypeTache.action:
        return 'task_alt';
      case TypeTache.formation:
        return 'school';
      case TypeTache.inventaire:
        return 'inventory';
      case TypeTache.promotion:
        return 'campaign';
      case TypeTache.maintenance:
        return 'build';
      case TypeTache.autre:
        return 'more_horiz';
    }
  }

  @override
  String toString() {
    return 'TacheMerchandising{id: $id, titre: $titre, type: $type, statut: $statut}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TacheMerchandising && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
