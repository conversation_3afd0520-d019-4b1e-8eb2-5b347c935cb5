# Requirements Document

## Introduction

This feature addresses the critical issue where Google Drive uploads in the rapport form are not working properly. Currently, the system falls back to simulation mode instead of performing real uploads to Google Drive. With the service account now properly configured and granted edit access to the target folder, we need to fix the upload implementation to ensure photos from rapport forms are successfully stored in Google Drive.

## Requirements

### Requirement 1

**User Story:** As a merchandiser creating a rapport, I want to upload photos that are automatically stored in Google Drive, so that my photos are safely backed up and accessible to administrators.

#### Acceptance Criteria

1. WHEN a merchandiser selects a photo from camera or gallery THEN the system SHALL upload the photo to the specified Google Drive folder
2. WHEN the upload is successful THEN the system SHALL display a success indicator (cloud icon) on the photo
3. WHEN the upload fails THEN the system SHALL display a clear error message explaining the failure
4. WHEN the upload is in progress THEN the system SHALL show a loading indicator
5. IF the upload fails THEN the system SHALL NOT save a fake/simulated URL to the rapport

### Requirement 2

**User Story:** As a system administrator, I want to verify that the Google Drive service is properly configured, so that I can troubleshoot upload issues effectively.

#### Acceptance Criteria

1. WHEN the app initializes THEN the system SHALL test the service account's access to the target folder
2. IF the service account lacks access THEN the system SHALL log a clear error message with instructions
3. WHEN testing folder access THEN the system SHALL verify both read and write permissions
4. IF folder access test fails THEN the system SHALL provide the service account email for sharing

### Requirement 3

**User Story:** As a merchandiser, I want to see the actual upload progress and status, so that I know whether my photos were successfully saved.

#### Acceptance Criteria

1. WHEN an upload starts THEN the system SHALL show a progress indicator with "Uploading..." text
2. WHEN an upload completes successfully THEN the system SHALL show a green cloud icon
3. WHEN an upload fails THEN the system SHALL show an error icon and error message
4. WHEN multiple photos are uploaded THEN each photo SHALL have its own independent status indicator
5. IF a photo upload fails THEN the user SHALL be able to retry the upload

### Requirement 4

**User Story:** As a developer, I want proper error handling and logging for Google Drive operations, so that I can diagnose and fix issues quickly.

#### Acceptance Criteria

1. WHEN any Google Drive operation fails THEN the system SHALL log the specific error details
2. WHEN authentication fails THEN the system SHALL log the authentication error with context
3. WHEN folder access is denied THEN the system SHALL log the folder ID and service account email
4. WHEN network errors occur THEN the system SHALL distinguish between network and API errors
5. IF the service account credentials are invalid THEN the system SHALL log a clear credential error

### Requirement 5

**User Story:** As a merchandiser, I want to delete photos from both the local form and Google Drive, so that I can remove unwanted photos completely.

#### Acceptance Criteria

1. WHEN a merchandiser deletes a photo from the rapport form THEN the system SHALL also delete it from Google Drive
2. WHEN deleting from Google Drive fails THEN the system SHALL log the error but still remove the photo from the form
3. WHEN a photo is successfully deleted THEN the system SHALL remove it from both the local photo list and URL list
4. IF a photo was never uploaded to Google Drive THEN deletion SHALL only affect the local form
5. WHEN deletion is in progress THEN the system SHALL show appropriate loading state