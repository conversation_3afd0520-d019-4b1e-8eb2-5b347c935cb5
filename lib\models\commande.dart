import 'commande_item.dart';

// Énumération des statuts de commande
enum StatutCommande {
  enAttente,
  confirmee,
  enPreparation,
  expediee,
  livree,
  annulee,
}

class Commande {
  final String? id;
  final String clientId;
  final DateTime dateCommande;
  final StatutCommande statut;
  final double montantTotal;
  final String? notes;
  final List<CommandeItem> items;

  Commande({
    this.id,
    required this.clientId,
    required this.dateCommande,
    this.statut = StatutCommande.enAttente,
    required this.montantTotal,
    this.notes,
    this.items = const [],
  });

  // Convertir une Commande en Map pour la base de données
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'clientId': clientId,
      'dateCommande': dateCommande.toIso8601String(),
      'statut': _getStatutString(statut),
      'montantTotal': montantTotal,
      'items': items.map((item) => item.toMap()).toList(),
    };

    // Only add notes if it's not null
    if (notes != null) {
      map['notes'] = notes;
    }

    return map;
  }

  // Convertir StatutCommande en string
  String _getStatutString(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return 'en_attente';
      case StatutCommande.confirmee:
        return 'confirmee';
      case StatutCommande.enPreparation:
        return 'en_preparation';
      case StatutCommande.expediee:
        return 'expediee';
      case StatutCommande.livree:
        return 'livree';
      case StatutCommande.annulee:
        return 'annulee';
    }
  }

  // Convertir string en StatutCommande
  static StatutCommande _getStatutFromString(dynamic statut) {
    // Handle both string and int for backward compatibility
    if (statut is int) {
      return StatutCommande.values[statut];
    }

    switch (statut.toString()) {
      case 'en_attente':
        return StatutCommande.enAttente;
      case 'confirmee':
        return StatutCommande.confirmee;
      case 'en_preparation':
        return StatutCommande.enPreparation;
      case 'expediee':
        return StatutCommande.expediee;
      case 'livree':
        return StatutCommande.livree;
      case 'annulee':
        return StatutCommande.annulee;
      default:
        return StatutCommande.enAttente;
    }
  }

  // Créer une Commande à partir d'une Map de la base de données
  factory Commande.fromMap(Map<String, dynamic> map) {
    List<CommandeItem> itemsList = [];

    // Parse items if they exist in the map
    if (map['items'] != null) {
      try {
        itemsList =
            (map['items'] as List)
                .map(
                  (item) => CommandeItem.fromMap(item as Map<String, dynamic>),
                )
                .toList();
      } catch (e) {
        print('Erreur lors du parsing des items: $e');
        itemsList = [];
      }
    }

    return Commande(
      id: map['id'],
      clientId: map['clientId'],
      dateCommande: DateTime.parse(map['dateCommande']),
      statut: _getStatutFromString(map['statut']),
      montantTotal: map['montantTotal'].toDouble(),
      notes: map['notes'],
      items: itemsList,
    );
  }

  // Créer une copie de la commande avec des modifications
  Commande copyWith({
    String? id,
    String? clientId,
    DateTime? dateCommande,
    StatutCommande? statut,
    double? montantTotal,
    String? notes,
    List<CommandeItem>? items,
  }) {
    return Commande(
      id: id ?? this.id,
      clientId: clientId ?? this.clientId,
      dateCommande: dateCommande ?? this.dateCommande,
      statut: statut ?? this.statut,
      montantTotal: montantTotal ?? this.montantTotal,
      notes: notes ?? this.notes,
      items: items ?? this.items,
    );
  }

  // Calculer le montant total à partir des items
  double calculerMontantTotal() {
    return items.fold(0.0, (total, item) => total + item.sousTotal);
  }

  // Nombre total d'articles
  int get nombreArticles =>
      items.fold(0, (total, item) => total + item.quantite);

  // Statut formaté
  String get statutFormate {
    switch (statut) {
      case StatutCommande.enAttente:
        return 'En attente';
      case StatutCommande.confirmee:
        return 'Confirmée';
      case StatutCommande.enPreparation:
        return 'En préparation';
      case StatutCommande.expediee:
        return 'Expédiée';
      case StatutCommande.livree:
        return 'Livrée';
      case StatutCommande.annulee:
        return 'Annulée';
    }
  }

  // Montant formaté
  String get montantFormate => '${montantTotal.toStringAsFixed(3)} DT';

  @override
  String toString() {
    return 'Commande{id: $id, clientId: $clientId, statut: $statut, montant: $montantTotal}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Commande && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
