import '../database/database_helper.dart';
import '../models/magasin.dart';

class MagasinService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Obtenir tous les magasins
  Future<List<Magasin>> obtenirTousMagasins() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('magasins');
    return List.generate(maps.length, (i) {
      return Magasin.fromMap(maps[i]);
    });
  }

  // Ajouter un magasin
  Future<int> ajouterMagasin(Magasin magasin) async {
    final db = await _databaseHelper.database;
    return await db.insert('magasins', magasin.toMap());
  }

  // Modifier un magasin
  Future<bool> modifierMagasin(Magasin magasin) async {
    final db = await _databaseHelper.database;
    final result = await db.update(
      'magasins',
      magasin.toMap(),
      where: 'id = ?',
      whereArgs: [magasin.id],
    );
    return result > 0;
  }

  // Supprimer un magasin
  Future<bool> supprimerMagasin(int id) async {
    final db = await _databaseHelper.database;
    final result = await db.delete(
      'magasins',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result > 0;
  }

  // Rechercher des magasins
  Future<List<Magasin>> rechercherMagasins(String terme) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'magasins',
      where:
          'nom LIKE ? OR adresse LIKE ? OR ville LIKE ? OR typeCommerce LIKE ?',
      whereArgs: ['%$terme%', '%$terme%', '%$terme%', '%$terme%'],
    );
    return List.generate(maps.length, (i) {
      return Magasin.fromMap(maps[i]);
    });
  }

  // Obtenir les statistiques
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    final db = await _databaseHelper.database;

    final nombreTotal = await db.rawQuery(
      'SELECT COUNT(*) as count FROM magasins',
    );
    final nombreActifs = await db.rawQuery(
      'SELECT COUNT(*) as count FROM magasins WHERE actif = 1',
    );
    final nombreInactifs = await db.rawQuery(
      'SELECT COUNT(*) as count FROM magasins WHERE actif = 0',
    );

    return {
      'nombreTotal': nombreTotal.first['count'],
      'nombreActifs': nombreActifs.first['count'],
      'nombreInactifs': nombreInactifs.first['count'],
    };
  }

  // Obtenir un magasin par ID
  Future<Magasin?> obtenirMagasinParId(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'magasins',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Magasin.fromMap(maps.first);
    }
    return null;
  }

  // Obtenir les magasins actifs
  Future<List<Magasin>> obtenirMagasinsActifs() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'magasins',
      where: 'actif = ?',
      whereArgs: [1],
    );
    return List.generate(maps.length, (i) {
      return Magasin.fromMap(maps[i]);
    });
  }

  // Obtenir les magasins par ville
  Future<List<Magasin>> obtenirMagasinsParVille(String ville) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'magasins',
      where: 'ville = ? AND actif = ?',
      whereArgs: [ville, 1],
    );
    return List.generate(maps.length, (i) {
      return Magasin.fromMap(maps[i]);
    });
  }

  // Obtenir les magasins par type de commerce
  Future<List<Magasin>> obtenirMagasinsParType(String type) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'magasins',
      where: 'typeCommerce = ? AND actif = ?',
      whereArgs: [type, 1],
    );
    return List.generate(maps.length, (i) {
      return Magasin.fromMap(maps[i]);
    });
  }

  // Obtenir les magasins dans un rayon donné (approximation simple)
  Future<List<Magasin>> obtenirMagasinsDansRayon(
    double latitude,
    double longitude,
    double rayonKm,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('magasins');

    List<Magasin> magasinsProches = [];
    for (Map<String, dynamic> map in maps) {
      final magasin = Magasin.fromMap(map);
      // Calcul approximatif de la distance (pour un calcul plus précis, utiliser une bibliothèque de géolocalisation)
      final distance = _calculerDistance(
        latitude,
        longitude,
        magasin.latitude,
        magasin.longitude,
      );
      if (distance <= rayonKm) {
        magasinsProches.add(magasin);
      }
    }

    return magasinsProches;
  }

  // Calcul approximatif de distance entre deux points (formule haversine simplifiée)
  double _calculerDistance(double lat1, double lon1, double lat2, double lon2) {
    const double R = 6371; // Rayon de la Terre en km
    final double dLat = _degreesEnRadians(lat2 - lat1);
    final double dLon = _degreesEnRadians(lon2 - lon1);

    final double a =
        (dLat / 2).abs() * (dLat / 2).abs() +
        (lat1 * 3.14159 / 180).abs() *
            (lat2 * 3.14159 / 180).abs() *
            (dLon / 2).abs() *
            (dLon / 2).abs();
    final double c = 2 * (a.abs()).abs();
    return R * c;
  }

  double _degreesEnRadians(double degrees) {
    return degrees * 3.14159 / 180;
  }
}
