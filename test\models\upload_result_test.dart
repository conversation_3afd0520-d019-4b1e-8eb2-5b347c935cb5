import 'package:flutter_test/flutter_test.dart';
import 'package:vitabrosse_pro/models/upload_result.dart';

void main() {
  group('UploadResult', () {
    test('should create successful result with factory constructor', () {
      final result = UploadResult.success(
        driveUrl: 'https://drive.google.com/file/d/test123/view',
        fileId: 'test123',
      );

      expect(result.success, isTrue);
      expect(result.driveUrl, equals('https://drive.google.com/file/d/test123/view'));
      expect(result.fileId, equals('test123'));
      expect(result.error, isNull);
    });

    test('should create failed result with factory constructor', () {
      final result = UploadResult.failure(
        error: 'Network connection failed',
      );

      expect(result.success, isFalse);
      expect(result.error, equals('Network connection failed'));
      expect(result.driveUrl, isNull);
      expect(result.fileId, isNull);
    });

    test('should have proper toString implementation', () {
      final successResult = UploadResult.success(
        driveUrl: 'https://drive.google.com/file/d/test123/view',
        fileId: 'test123',
      );
      
      final failureResult = UploadResult.failure(
        error: 'Upload failed',
      );

      expect(
        successResult.toString(),
        contains('UploadResult.success'),
      );
      expect(
        failureResult.toString(),
        contains('UploadResult.failure'),
      );
    });

    test('should implement equality correctly', () {
      final result1 = UploadResult.success(
        driveUrl: 'https://drive.google.com/file/d/test123/view',
        fileId: 'test123',
      );
      
      final result2 = UploadResult.success(
        driveUrl: 'https://drive.google.com/file/d/test123/view',
        fileId: 'test123',
      );

      final result3 = UploadResult.failure(error: 'Different error');

      expect(result1, equals(result2));
      expect(result1, isNot(equals(result3)));
    });

    test('should have consistent hashCode', () {
      final result1 = UploadResult.success(
        driveUrl: 'https://drive.google.com/file/d/test123/view',
        fileId: 'test123',
      );
      
      final result2 = UploadResult.success(
        driveUrl: 'https://drive.google.com/file/d/test123/view',
        fileId: 'test123',
      );

      expect(result1.hashCode, equals(result2.hashCode));
    });
  });
}