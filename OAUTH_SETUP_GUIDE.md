# Google OAuth Setup Guide for VitaBrosse Pro

## Current Issues
Your app is failing with OAuth configuration errors. Here's how to fix them:

## Step 1: Get Your App's SHA-1 Fingerprint

Run this command in your project root:
```bash
cd android
./gradlew signingReport
```

Look for the SHA-1 fingerprint under "Variant: debug" -> "Store: ~/.android/debug.keystore"

## Step 2: Configure Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select project: `vitabrosseapp-467110`
3. Go to "APIs & Services" > "Credentials"
4. Find your OAuth 2.0 client ID: `************-8oolv1cf9u61mdsqv47i9lqvt75qq4ru.apps.googleusercontent.com`
5. Click "Edit OAuth client"
6. Add these details:
   - **Package name**: `com.example.commercial`
   - **SHA-1 certificate fingerprint**: [Your SHA-1 from step 1]

## Step 3: Update google-services.json

1. Download the updated `google-services.json` from Google Cloud Console
2. Replace the file in `android/app/google-services.json`

## Step 4: Verify Package Name

Check that your `android/app/build.gradle.kts` has:
```kotlin
android {
    namespace = "com.example.commercial"
    // ...
}
```

## Step 5: Test OAuth

After making these changes:
1. Clean and rebuild your app
2. Test the photo upload functionality
3. Check the console logs for OAuth success messages