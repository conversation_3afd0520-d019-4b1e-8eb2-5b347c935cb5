class Magasin {
  final int? id;
  final String nom;
  final String adresse;
  final String ville;
  final String codePostal;
  final String telephone;
  final String? email;
  final String typeCommerce;
  final double latitude;
  final double longitude;
  final bool actif;
  final DateTime dateCreation;

  Magasin({
    this.id,
    required this.nom,
    required this.adresse,
    required this.ville,
    required this.codePostal,
    required this.telephone,
    this.email,
    required this.typeCommerce,
    required this.latitude,
    required this.longitude,
    this.actif = true,
    required this.dateCreation,
  });

  // Convertir un Magasin en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nom': nom,
      'adresse': adresse,
      'ville': ville,
      'codePostal': codePostal,
      'telephone': telephone,
      'email': email,
      'typeCommerce': typeCommerce,
      'latitude': latitude,
      'longitude': longitude,
      'actif': actif ? 1 : 0,
      'dateCreation': dateCreation.toIso8601String(),
    };
  }

  // Créer un Magasin à partir d'une Map de la base de données
  factory Magasin.fromMap(Map<String, dynamic> map) {
    return Magasin(
      id: map['id'],
      nom: map['nom'],
      adresse: map['adresse'],
      ville: map['ville'],
      codePostal: map['codePostal'],
      telephone: map['telephone'],
      email: map['email'],
      typeCommerce: map['typeCommerce'],
      latitude: map['latitude'].toDouble(),
      longitude: map['longitude'].toDouble(),
      actif: map['actif'] == 1,
      dateCreation: DateTime.parse(map['dateCreation']),
    );
  }

  // Créer une copie du magasin avec des modifications
  Magasin copyWith({
    int? id,
    String? nom,
    String? adresse,
    String? ville,
    String? codePostal,
    String? telephone,
    String? email,
    String? typeCommerce,
    double? latitude,
    double? longitude,
    bool? actif,
    DateTime? dateCreation,
  }) {
    return Magasin(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      adresse: adresse ?? this.adresse,
      ville: ville ?? this.ville,
      codePostal: codePostal ?? this.codePostal,
      telephone: telephone ?? this.telephone,
      email: email ?? this.email,
      typeCommerce: typeCommerce ?? this.typeCommerce,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      actif: actif ?? this.actif,
      dateCreation: dateCreation ?? this.dateCreation,
    );
  }

  // Adresse complète du magasin
  String get adresseComplete => '$adresse, $codePostal $ville';

  @override
  String toString() {
    return 'Magasin{id: $id, nom: $nom, ville: $ville}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Magasin && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
