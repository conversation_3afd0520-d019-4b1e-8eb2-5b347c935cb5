import 'dart:io';
import 'dart:typed_data';
import '../models/upload_result.dart';
import '../models/photo_upload_state.dart';
import '../models/upload_status.dart';
import 'google_drive_oauth_service.dart';
import 'google_drive_service.dart';
import 'direct_drive_upload_service.dart';

/// Unified photo upload service that handles both OAuth and Service Account methods
class PhotoUploadService {
  static bool _initialized = false;
  
  /// Initialize the upload service
  static Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      print('🔧 Initializing Photo Upload Service...');
      
      // Initialize Direct Drive Upload Service first (most reliable)
      await DirectDriveUploadService.initialize();
      print('✅ Direct Drive Upload Service initialized');
      
      // Try OAuth as secondary option
      try {
        final oauthInitialized = await GoogleDriveOAuthService.initialize();
        if (oauthInitialized) {
          print('✅ OAuth service also available');
        }
      } catch (e) {
        print('⚠️ OAuth not available: $e');
      }
      
      // Initialize Service Account as final fallback
      try {
        await GoogleDriveService.initialize();
        print('✅ Service Account also available');
      } catch (e) {
        print('⚠️ Service Account not available: $e');
      }
      
      _initialized = true;
      print('✅ Photo Upload Service initialized successfully');
    } catch (e) {
      print('❌ Error initializing Photo Upload Service: $e');
      _initialized = true; // Continue anyway to allow app to function
    }
  }
  
  /// Upload a photo file with automatic fallback
  static Future<UploadResult> uploadPhoto(File imageFile, String fileName) async {
    try {
      await initialize();
      
      print('📤 Starting photo upload: $fileName');
      
      // Validate file
      if (!await imageFile.exists()) {
        return UploadResult.failure(error: 'File does not exist');
      }
      
      final bytes = await imageFile.readAsBytes();
      if (bytes.length > 10 * 1024 * 1024) {
        return UploadResult.failure(error: 'File too large (max 10MB)');
      }
      
      // Try Direct Drive Upload first (most reliable with public folder)
      try {
        print('📁 Using Direct Drive Upload (public folder)...');
        final result = await DirectDriveUploadService.uploadPhoto(imageFile, fileName);
        if (result.success) {
          print('✅ Direct upload successful');
          return result;
        }
        print('⚠️ Direct upload failed: ${result.error}');
      } catch (e) {
        print('⚠️ Direct upload error: $e');
      }
      
      // Try OAuth method as fallback
      if (GoogleDriveOAuthService.isSignedIn) {
        print('🔐 Using OAuth upload method...');
        final result = await GoogleDriveOAuthService.uploadImage(imageFile, fileName);
        if (result.success) {
          return result;
        }
        print('⚠️ OAuth upload failed: ${result.error}');
      }
      
      // Final fallback to Service Account method
      print('🔄 Using Service Account fallback...');
      return await GoogleDriveService.uploadImage(imageFile, fileName);
      
    } catch (e) {
      print('❌ Photo upload failed: $e');
      return UploadResult.failure(error: 'Upload failed: $e');
    }
  }
  
  /// Upload photo from bytes with automatic fallback
  static Future<UploadResult> uploadPhotoFromBytes(
    Uint8List imageBytes, 
    String fileName,
  ) async {
    try {
      await initialize();
      
      print('📤 Starting photo upload from bytes: $fileName');
      
      if (imageBytes.length > 10 * 1024 * 1024) {
        return UploadResult.failure(error: 'File too large (max 10MB)');
      }
      
      // Try Direct Drive Upload first (most reliable with public folder)
      try {
        print('📁 Using Direct Drive Upload (public folder)...');
        final result = await DirectDriveUploadService.uploadPhotoFromBytes(imageBytes, fileName);
        if (result.success) {
          print('✅ Direct bytes upload successful');
          return result;
        }
        print('⚠️ Direct bytes upload failed: ${result.error}');
      } catch (e) {
        print('⚠️ Direct bytes upload error: $e');
      }
      
      // Try OAuth method as fallback
      if (GoogleDriveOAuthService.isSignedIn) {
        print('🔐 Using OAuth upload method...');
        final result = await GoogleDriveOAuthService.uploadImageFromBytes(imageBytes, fileName);
        if (result.success) {
          return result;
        }
        print('⚠️ OAuth upload failed: ${result.error}');
      }
      
      // Final fallback to Service Account method
      print('🔄 Using Service Account fallback...');
      final driveUrl = await GoogleDriveService.uploadImageFromBytes(
        imageBytes, 
        fileName, 
        'image/jpeg',
      );
      return UploadResult.success(driveUrl: driveUrl, fileId: _extractFileId(driveUrl));
      
    } catch (e) {
      print('❌ Photo upload from bytes failed: $e');
      return UploadResult.failure(error: 'Upload failed: $e');
    }
  }
  
  /// Delete a photo with automatic method detection
  static Future<bool> deletePhoto(String driveUrl) async {
    try {
      // Try Direct Drive Upload method first
      try {
        print('🗑️ Using Direct Drive Upload for deletion...');
        final result = await DirectDriveUploadService.deletePhoto(driveUrl);
        if (result) {
          print('✅ Direct deletion successful');
          return result;
        }
        print('⚠️ Direct deletion failed');
      } catch (e) {
        print('⚠️ Direct deletion error: $e');
      }
      
      // Try OAuth method as fallback
      if (GoogleDriveOAuthService.isSignedIn) {
        print('🔐 Using OAuth deletion method...');
        return await GoogleDriveOAuthService.deleteImage(driveUrl);
      }
      
      // Final fallback to Service Account method
      print('🔄 Using Service Account deletion fallback...');
      return await GoogleDriveService.deleteImage(driveUrl);
      
    } catch (e) {
      print('❌ Photo deletion failed: $e');
      return false;
    }
  }
  
  /// Sign in with Google (for OAuth method)
  static Future<bool> signInWithGoogle() async {
    try {
      return await GoogleDriveOAuthService.signIn();
    } catch (e) {
      print('❌ Google sign-in failed: $e');
      return false;
    }
  }
  
  /// Check if user is signed in
  static bool get isSignedIn => GoogleDriveOAuthService.isSignedIn;
  
  /// Get current user email
  static String? get userEmail => GoogleDriveOAuthService.userEmail;
  
  /// Sign out
  static Future<void> signOut() async {
    await GoogleDriveOAuthService.signOut();
  }
  
  /// Clean photo URLs (remove local paths, keep only Drive URLs)
  static List<String> cleanPhotoUrls(List<String> photos) {
    return photos
        .where((photo) => photo.startsWith('https://drive.google.com/'))
        .toList();
  }
  
  /// Extract file ID from Google Drive URL
  static String _extractFileId(String driveUrl) {
    final regex = RegExp(r'/file/d/([a-zA-Z0-9-_]+)');
    final match = regex.firstMatch(driveUrl);
    return match?.group(1) ?? 'unknown';
  }
  
  /// Get upload status message for user
  static String getUploadStatusMessage(PhotoUploadState state) {
    switch (state.status) {
      case UploadStatus.idle:
        return 'Prêt pour l\'upload';
      case UploadStatus.uploading:
        return 'Upload en cours...';
      case UploadStatus.success:
        return 'Upload réussi';
      case UploadStatus.failed:
        return state.userFriendlyError;
      case UploadStatus.retrying:
        return 'Nouvel essai...';
    }
  }
}