import '../models/devis.dart';
import '../models/commande.dart';
import '../models/commande_item.dart';
import '../services/firebase_service.dart';
import '../services/commande_service.dart';

class DevisService {
  // Créer un nouveau devis
  Future<String> creerDevis(Devis devis) async {
    try {
      final docRef = await FirebaseService.devis.add(devis.toMap());
      return docRef.id;
    } catch (e) {
      throw Exception('Erreur lors de la création du devis: $e');
    }
  }

  // Modifier un devis existant
  Future<void> modifierDevis(Devis devis) async {
    try {
      if (devis.id == null) {
        throw Exception('ID du devis requis pour la modification');
      }
      await FirebaseService.devis.doc(devis.id!).update(devis.toMap());
    } catch (e) {
      throw Exception('Erreur lors de la modification du devis: $e');
    }
  }

  // Obtenir tous les devis
  Future<List<Devis>> obtenirTousLesDevis() async {
    try {
      final snapshot =
          await FirebaseService.devis
              .orderBy('dateCreation', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Devis.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des devis: $e');
    }
  }

  // Obtenir un devis par ID
  Future<Devis?> obtenirDevisParId(String id) async {
    try {
      final doc = await FirebaseService.devis.doc(id).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return Devis.fromMap({...data, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception('Erreur lors de la récupération du devis: $e');
    }
  }

  // Obtenir les devis d'un client
  Future<List<Devis>> obtenirDevisParClient(String clientId) async {
    try {
      final snapshot =
          await FirebaseService.devis
              .where('clientId', isEqualTo: clientId)
              .orderBy('dateDevis', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Devis.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des devis du client: $e');
    }
  }

  // Mettre à jour le statut d'un devis
  Future<void> mettreAJourStatut(String devisId, String nouveauStatut) async {
    try {
      await FirebaseService.devis.doc(devisId).update({
        'statut': nouveauStatut,
      });
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du statut: $e');
    }
  }

  // Obtenir les devis par statut
  Future<List<Devis>> obtenirDevisParStatut(String statut) async {
    try {
      final snapshot =
          await FirebaseService.devis
              .where('statut', isEqualTo: statut)
              .orderBy('dateDevis', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return Devis.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des devis par statut: $e',
      );
    }
  }

  // Compter le nombre total de devis
  Future<int> obtenirNombreDevis() async {
    try {
      final snapshot = await FirebaseService.devis.get();
      return snapshot.docs.length;
    } catch (e) {
      throw Exception('Erreur lors du comptage des devis: $e');
    }
  }

  // Obtenir les statistiques des devis
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      final snapshot = await FirebaseService.devis.get();
      final devisList =
          snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return Devis.fromMap({...data, 'id': doc.id});
          }).toList();

      final total = devisList.length;

      return {'total': total};
    } catch (e) {
      throw Exception('Erreur lors de la récupération des statistiques: $e');
    }
  }

  // Supprimer un devis
  Future<void> supprimerDevis(String id) async {
    try {
      await FirebaseService.devis.doc(id).delete();
    } catch (e) {
      throw Exception('Erreur lors de la suppression du devis: $e');
    }
  }

  // Dupliquer un devis
  Future<String> dupliquerDevis(String id) async {
    try {
      final devis = await obtenirDevisParId(id);
      if (devis == null) {
        throw Exception('Devis non trouvé');
      }

      final nouveauDevis = devis.copyWith(
        id: null,
        numero: await genererNumeroDevis(),
        dateCreation: DateTime.now(),
      );

      return await creerDevis(nouveauDevis);
    } catch (e) {
      throw Exception('Erreur lors de la duplication du devis: $e');
    }
  }

  // Transformer un devis en commande
  Future<String> transformerEnCommande(String devisId) async {
    try {
      final devis = await obtenirDevisParId(devisId);
      if (devis == null) {
        throw Exception('Devis non trouvé');
      }

      // Vérifier que le devis contient des items
      if (devis.items.isEmpty) {
        throw Exception('Le devis ne contient aucun article');
      }

      // Vérifier que toutes les données nécessaires sont présentes
      for (final item in devis.items) {
        if (item.produitId.isEmpty ||
            item.designation.isEmpty ||
            item.quantite <= 0) {
          throw Exception('Données incomplètes dans les articles du devis');
        }
      }

      // Créer les items de commande à partir des items de devis
      final commandeItems =
          devis.items
              .map(
                (devisItem) => CommandeItem(
                  produitId: devisItem.produitId,
                  nomProduit:
                      devisItem.designation, // Utilise designation comme nom
                  codeProduit:
                      devisItem.reference, // Utilise reference comme code
                  prixUnitaire: devisItem.prixUnitaireHT,
                  quantite: devisItem.quantite,
                  sousTotal: devisItem.sousTotal,
                  unite: devisItem.unite,
                ),
              )
              .toList();

      // Créer la commande à partir du devis
      final commande = Commande(
        clientId: devis.clientId,
        dateCommande: DateTime.now(),
        statut: StatutCommande.enAttente,
        montantTotal: devis.totalTTC,
        notes:
            'Commande créée à partir du devis ${devis.numeroFormate}${devis.notes != null ? '\n\nNotes du devis: ${devis.notes}' : ''}',
        items: commandeItems,
      );

      // Sauvegarder la commande dans la base de données
      final commandeService = CommandeService();
      final commandeId = await commandeService.creerCommande(commande);

      // Mettre à jour le devis pour indiquer qu'il a été transformé
      await FirebaseService.devis.doc(devisId).update({
        'dateReponse': DateTime.now().toIso8601String(),
        'notes':
            'Transformé en commande $commandeId${devis.notes != null ? '\n\nNotes originales: ${devis.notes}' : ''}',
      });

      print(
        'Devis ${devis.numeroFormate} transformé en commande avec succès. Commande ID: $commandeId',
      );
      return commandeId;
    } catch (e) {
      throw Exception('Erreur lors de la transformation en commande: $e');
    }
  }

  // Générer un numéro de devis unique
  Future<String> genererNumeroDevis() async {
    try {
      final now = DateTime.now();
      final year = now.year.toString().substring(2);
      final month = now.month.toString().padLeft(2, '0');

      // Obtenir le dernier numéro du mois
      final prefix = 'DEV$year$month';
      final snapshot =
          await FirebaseService.devis
              .where('numero', isGreaterThanOrEqualTo: prefix)
              .where('numero', isLessThan: '${prefix}Z')
              .orderBy('numero', descending: true)
              .limit(1)
              .get();

      int numero = 1;
      if (snapshot.docs.isNotEmpty) {
        final data = snapshot.docs.first.data() as Map<String, dynamic>;
        final lastNumero = data['numero'] as String;
        final lastNumber =
            int.tryParse(lastNumero.substring(prefix.length)) ?? 0;
        numero = lastNumber + 1;
      }

      return '$prefix${numero.toString().padLeft(3, '0')}';
    } catch (e) {
      throw Exception('Erreur lors de la génération du numéro de devis: $e');
    }
  }
}
