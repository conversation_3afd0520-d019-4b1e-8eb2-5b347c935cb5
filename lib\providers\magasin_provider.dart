import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/magasin.dart';
import '../services/magasin_service.dart';

class MagasinProvider extends ChangeNotifier {
  final MagasinService _magasinService = MagasinService();

  List<Magasin> _magasins = [];
  bool _isLoading = false;
  String? _error;

  List<Magasin> get magasins => _magasins;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Charger tous les magasins
  Future<void> chargerMagasins() async {
    _setLoading(true);
    try {
      _magasins = await _magasinService.obtenirTousMagasins();
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des magasins: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Ajouter un magasin
  Future<bool> ajouterMagasin(Magasin magasin) async {
    try {
      final id = await _magasinService.ajouterMagasin(magasin);
      if (id > 0) {
        _magasins.add(magasin.copyWith(id: id));
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de l\'ajout du magasin: $e';
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return false;
    }
  }

  // Modifier un magasin
  Future<bool> modifierMagasin(Magasin magasin) async {
    try {
      final success = await _magasinService.modifierMagasin(magasin);
      if (success) {
        final index = _magasins.indexWhere((m) => m.id == magasin.id);
        if (index != -1) {
          _magasins[index] = magasin;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            notifyListeners();
          });
        }
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la modification du magasin: $e';
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return false;
    }
  }

  // Supprimer un magasin
  Future<bool> supprimerMagasin(int id) async {
    try {
      final success = await _magasinService.supprimerMagasin(id);
      if (success) {
        _magasins.removeWhere((m) => m.id == id);
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la suppression du magasin: $e';
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return false;
    }
  }

  // Rechercher des magasins
  Future<void> rechercherMagasins(String terme) async {
    _setLoading(true);
    try {
      _magasins = await _magasinService.rechercherMagasins(terme);
      _error = null;
    } catch (e) {
      _error = 'Erreur lors de la recherche: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Obtenir les statistiques
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      return await _magasinService.obtenirStatistiques();
    } catch (e) {
      return {'nombreTotal': 0, 'nombreActifs': 0, 'nombreInactifs': 0};
    }
  }

  // Obtenir un magasin par ID
  Magasin? obtenirMagasinParId(int id) {
    try {
      return _magasins.firstWhere((m) => m.id == id);
    } catch (e) {
      return null;
    }
  }

  // Obtenir les magasins par ville
  List<Magasin> obtenirMagasinsParVille(String ville) {
    return _magasins
        .where((m) => m.ville.toLowerCase().contains(ville.toLowerCase()))
        .toList();
  }

  // Obtenir les magasins par type de commerce
  List<Magasin> obtenirMagasinsParType(String type) {
    return _magasins
        .where((m) => m.typeCommerce.toLowerCase().contains(type.toLowerCase()))
        .toList();
  }

  // Activer/Désactiver un magasin
  Future<bool> toggleActivation(int id) async {
    try {
      final magasin = obtenirMagasinParId(id);
      if (magasin != null) {
        final magasinModifie = magasin.copyWith(actif: !magasin.actif);
        return await modifierMagasin(magasinModifie);
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la modification du statut: $e';
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return false;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  void effacerErreur() {
    _error = null;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
}
