/// Represents the result of a Google Drive upload operation
class UploadResult {
  final bool success;
  final String? driveUrl;
  final String? error;
  final String? fileId;
  final DateTime timestamp;

  UploadResult({
    required this.success,
    this.driveUrl,
    this.error,
    this.fileId,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Create a successful upload result
  factory UploadResult.success({
    required String driveUrl,
    required String fileId,
  }) {
    return UploadResult(
      success: true,
      driveUrl: driveUrl,
      fileId: fileId,
    );
  }

  /// Create a failed upload result
  factory UploadResult.failure({
    required String error,
  }) {
    return UploadResult(
      success: false,
      error: error,
    );
  }

  @override
  String toString() {
    if (success) {
      return 'UploadResult.success(driveUrl: $driveUrl, fileId: $fileId)';
    } else {
      return 'UploadResult.failure(error: $error)';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UploadResult &&
        other.success == success &&
        other.driveUrl == driveUrl &&
        other.error == error &&
        other.fileId == fileId;
  }

  @override
  int get hashCode {
    return success.hashCode ^
        driveUrl.hashCode ^
        error.hashCode ^
        fileId.hashCode;
  }
}