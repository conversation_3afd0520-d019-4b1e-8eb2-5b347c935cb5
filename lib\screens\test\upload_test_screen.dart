import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../services/photo_upload_service.dart';
import '../../services/photo_upload_service.dart';
import '../../services/google_drive_oauth_service.dart';
import '../../services/google_drive_service.dart';
import '../../services/direct_drive_upload_service.dart';
import '../../models/upload_result.dart';

class UploadTestScreen extends StatefulWidget {
  const UploadTestScreen({super.key});

  @override
  State<UploadTestScreen> createState() => _UploadTestScreenState();
}

class _UploadTestScreenState extends State<UploadTestScreen> {
  final List<String> _logs = [];
  bool _isLoading = false;
  final ImagePicker _picker = ImagePicker();

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toIso8601String().substring(11, 19)}: $message');
    });
    print(message);
  }

  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }

  Future<void> _testOAuthConfiguration() async {
    _addLog('🔧 Testing OAuth Configuration...');
    setState(() => _isLoading = true);

    try {
      // Test OAuth initialization
      final initialized = await GoogleDriveOAuthService.initialize();
      _addLog('OAuth Initialized: $initialized');

      // Check if user is signed in
      final isSignedIn = GoogleDriveOAuthService.isSignedIn;
      _addLog('User Signed In: $isSignedIn');

      if (isSignedIn) {
        _addLog('User Email: ${GoogleDriveOAuthService.userEmail}');
        _addLog('User Name: ${GoogleDriveOAuthService.userName}');
      }

      _addLog('✅ OAuth configuration test completed');
    } catch (e) {
      _addLog('❌ OAuth test failed: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testServiceAccountConfiguration() async {
    _addLog('🔧 Testing Service Account Configuration...');
    setState(() => _isLoading = true);

    try {
      // Test Service Account initialization
      await GoogleDriveService.initialize();
      _addLog('✅ Service Account initialized successfully');
    } catch (e) {
      _addLog('❌ Service Account test failed: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testPhotoUploadService() async {
    _addLog('🔧 Testing Photo Upload Service...');
    setState(() => _isLoading = true);

    try {
      // Test Photo Upload Service initialization
      await PhotoUploadService.initialize();
      _addLog('✅ Photo Upload Service initialized successfully');

      // Check service status
      final isSignedIn = PhotoUploadService.isSignedIn;
      _addLog('Service Signed In: $isSignedIn');

      if (isSignedIn) {
        _addLog('Service User Email: ${PhotoUploadService.userEmail}');
      }
    } catch (e) {
      _addLog('❌ Photo Upload Service test failed: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testDirectDriveUpload() async {
    _addLog('📁 Testing Direct Drive Upload Service...');
    setState(() => _isLoading = true);

    try {
      // Test Direct Drive Upload Service initialization
      await DirectDriveUploadService.initialize();
      _addLog('✅ Direct Drive Upload Service initialized successfully');

      // Display upload info
      final uploadInfo = DirectDriveUploadService.uploadInfo;
      _addLog('📁 Folder ID: ${uploadInfo['folder_id']}');
      _addLog('🔗 Folder URL: ${uploadInfo['folder_url']}');
      _addLog('📊 Status: ${uploadInfo['status']}');
      _addLog('📝 Description: ${uploadInfo['description']}');

      _addLog('✅ Direct Drive Upload Service test completed');
    } catch (e) {
      _addLog('❌ Direct Drive Upload Service test failed: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testGoogleSignIn() async {
    _addLog('🔐 Testing Google Sign In...');
    setState(() => _isLoading = true);

    try {
      final success = await PhotoUploadService.signInWithGoogle();
      if (success) {
        _addLog('✅ Google Sign In successful');
        _addLog('User Email: ${PhotoUploadService.userEmail}');
      } else {
        _addLog('❌ Google Sign In failed');
      }
    } catch (e) {
      _addLog('❌ Google Sign In error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testPhotoUpload() async {
    _addLog('📤 Testing Photo Upload...');
    setState(() => _isLoading = true);

    try {
      // Pick an image
      final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
      if (image == null) {
        _addLog('❌ No image selected');
        return;
      }

      _addLog('📁 Image selected: ${image.name}');
      final file = File(image.path);
      final fileSize = await file.length();
      _addLog('📁 File size: ${fileSize} bytes');

      // Test upload
      final fileName = 'test_${DateTime.now().millisecondsSinceEpoch}.${image.path.split('.').last}';
      final result = await PhotoUploadService.uploadPhoto(file, fileName);

      if (result.success) {
        _addLog('✅ Upload successful!');
        _addLog('📎 Drive URL: ${result.driveUrl}');
        _addLog('🆔 File ID: ${result.fileId}');
      } else {
        _addLog('❌ Upload failed: ${result.error}');
      }
    } catch (e) {
      _addLog('❌ Photo upload test error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testCameraUpload() async {
    _addLog('📷 Testing Camera Upload...');
    setState(() => _isLoading = true);

    try {
      // Take a photo
      final XFile? image = await _picker.pickImage(source: ImageSource.camera);
      if (image == null) {
        _addLog('❌ No photo taken');
        return;
      }

      _addLog('📁 Photo taken: ${image.name}');
      final file = File(image.path);
      final fileSize = await file.length();
      _addLog('📁 File size: ${fileSize} bytes');

      // Test upload
      final fileName = 'camera_${DateTime.now().millisecondsSinceEpoch}.${image.path.split('.').last}';
      final result = await PhotoUploadService.uploadPhoto(file, fileName);

      if (result.success) {
        _addLog('✅ Camera upload successful!');
        _addLog('📎 Drive URL: ${result.driveUrl}');
        _addLog('🆔 File ID: ${result.fileId}');
      } else {
        _addLog('❌ Camera upload failed: ${result.error}');
      }
    } catch (e) {
      _addLog('❌ Camera upload test error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upload Test & Debug'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _clearLogs,
            icon: const Icon(Icons.clear_all),
            tooltip: 'Clear Logs',
          ),
        ],
      ),
      body: Column(
        children: [
          // Test Buttons
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  'Configuration Tests',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testOAuthConfiguration,
                      child: const Text('Test OAuth'),
                    ),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testServiceAccountConfiguration,
                      child: const Text('Test Service Account'),
                    ),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testPhotoUploadService,
                      child: const Text('Test Upload Service'),
                    ),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testDirectDriveUpload,
                      child: const Text('Test Direct Drive'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Text(
                  'Authentication Tests',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testGoogleSignIn,
                  child: const Text('Test Google Sign In'),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Upload Tests',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testPhotoUpload,
                      child: const Text('Test Gallery Upload'),
                    ),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testCameraUpload,
                      child: const Text('Test Camera Upload'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const Divider(),
          // Loading indicator
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: LinearProgressIndicator(),
            ),
          // Logs
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Debug Logs',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: ListView.builder(
                        itemCount: _logs.length,
                        itemBuilder: (context, index) {
                          final log = _logs[index];
                          Color textColor = Colors.black87;
                          if (log.contains('❌')) {
                            textColor = Colors.red;
                          } else if (log.contains('✅')) {
                            textColor = Colors.green;
                          } else if (log.contains('⚠️')) {
                            textColor = Colors.orange;
                          }
                          
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Text(
                              log,
                              style: TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                                color: textColor,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}