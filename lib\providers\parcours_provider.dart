import 'package:flutter/foundation.dart';
import '../models/parcours.dart';
import '../services/parcours_service.dart';

class ParcoursProvider extends ChangeNotifier {
  final ParcoursService _parcoursService = ParcoursService();

  List<Parcours> _parcours = [];
  bool _isLoading = false;
  String? _error;

  List<Parcours> get parcours => _parcours;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Charger tous les parcours
  Future<void> chargerParcours() async {
    _setLoading(true);
    try {
      _parcours = await _parcoursService.obtenirTousParcours();
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des parcours: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Ajouter un parcours
  Future<bool> ajouterParcours(Parcours parcours) async {
    try {
      final id = await _parcoursService.ajouterParcours(parcours);
      if (id > 0) {
        _parcours.add(parcours.copyWith(id: id));
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de l\'ajout du parcours: $e';
      notifyListeners();
      return false;
    }
  }

  // Modifier un parcours
  Future<bool> modifierParcours(Parcours parcours) async {
    try {
      final success = await _parcoursService.modifierParcours(parcours);
      if (success) {
        final index = _parcours.indexWhere((p) => p.id == parcours.id);
        if (index != -1) {
          _parcours[index] = parcours;
          notifyListeners();
        }
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la modification du parcours: $e';
      notifyListeners();
      return false;
    }
  }

  // Supprimer un parcours
  Future<bool> supprimerParcours(int id) async {
    try {
      final success = await _parcoursService.supprimerParcours(id);
      if (success) {
        _parcours.removeWhere((p) => p.id == id);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la suppression du parcours: $e';
      notifyListeners();
      return false;
    }
  }

  // Rechercher des parcours
  Future<void> rechercherParcours(String terme) async {
    _setLoading(true);
    try {
      _parcours = await _parcoursService.rechercherParcours(terme);
      _error = null;
    } catch (e) {
      _error = 'Erreur lors de la recherche: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Obtenir les statistiques
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      return await _parcoursService.obtenirStatistiques();
    } catch (e) {
      return {
        'nombreTotal': 0,
        'planifies': 0,
        'enCours': 0,
        'termines': 0,
        'annules': 0,
      };
    }
  }

  // Obtenir un parcours par ID
  Parcours? obtenirParcoursParId(int id) {
    try {
      return _parcours.firstWhere((p) => p.id == id);
    } catch (e) {
      return null;
    }
  }

  // Obtenir les parcours par merchandiser
  List<Parcours> obtenirParcoursParMerchandiser(int merchandiserId) {
    return _parcours.where((p) => p.merchandiserId == merchandiserId).toList();
  }

  // Obtenir les parcours par date
  List<Parcours> obtenirParcoursParDate(DateTime date) {
    return _parcours
        .where(
          (p) =>
              p.dateVisite.year == date.year &&
              p.dateVisite.month == date.month &&
              p.dateVisite.day == date.day,
        )
        .toList();
  }

  // Obtenir les parcours par statut
  List<Parcours> obtenirParcoursParStatut(String statut) {
    return _parcours.where((p) => p.statut == statut).toList();
  }

  // Démarrer un parcours
  Future<bool> demarrerParcours(int id) async {
    try {
      final parcours = obtenirParcoursParId(id);
      if (parcours != null && parcours.statut == 'planifie') {
        final parcoursModifie = parcours.copyWith(
          statut: 'en_cours',
          dateDebut: DateTime.now(),
        );
        return await modifierParcours(parcoursModifie);
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors du démarrage du parcours: $e';
      notifyListeners();
      return false;
    }
  }

  // Terminer un parcours
  Future<bool> terminerParcours(int id, {String? commentaires}) async {
    try {
      final parcours = obtenirParcoursParId(id);
      if (parcours != null && parcours.statut == 'en_cours') {
        final parcoursModifie = parcours.copyWith(
          statut: 'termine',
          dateFin: DateTime.now(),
          commentaires: commentaires,
        );
        return await modifierParcours(parcoursModifie);
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la finalisation du parcours: $e';
      notifyListeners();
      return false;
    }
  }

  // Annuler un parcours
  Future<bool> annulerParcours(int id, {String? commentaires}) async {
    try {
      final parcours = obtenirParcoursParId(id);
      if (parcours != null &&
          (parcours.statut == 'planifie' || parcours.statut == 'en_cours')) {
        final parcoursModifie = parcours.copyWith(
          statut: 'annule',
          commentaires: commentaires,
        );
        return await modifierParcours(parcoursModifie);
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de l\'annulation du parcours: $e';
      notifyListeners();
      return false;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void effacerErreur() {
    _error = null;
    notifyListeners();
  }
}
