import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../providers/firebase_client_provider.dart';
import '../../models/client.dart';
import '../../widgets/vitabrosse_logo.dart';
import '../../widgets/professional_ui_components.dart';
import 'client_form_screen.dart';
import 'client_detail_screen.dart';

class ClientsScreen extends StatefulWidget {
  const ClientsScreen({super.key});

  @override
  State<ClientsScreen> createState() => _ClientsScreenState();
}

class _ClientsScreenState extends State<ClientsScreen>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();
  bool _isDisposed = false;

  @override
  bool get wantKeepAlive => true; // Keep the state alive when navigating away

  @override
  void initState() {
    super.initState();
    // Load clients when the screen is first opened
    _loadClients();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _searchController.dispose();
    super.dispose();
  }

  void _loadClients() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isDisposed) {
        context.read<FirebaseClientProvider>().loadClients();
      }
    });
  }

  // Add a method to refresh data when the tab becomes active
  void refreshData() {
    if (mounted && !_isDisposed) {
      context.read<FirebaseClientProvider>().refreshClients();
    }
  }

  // Methods for contact actions
  Future<void> _makePhoneCall(String phoneNumber) async {
    final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    final uri = Uri.parse('tel:$cleanPhone');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted && !_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Impossible d\'ouvrir l\'application téléphone'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _openWhatsApp(String phoneNumber) async {
    final cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    // Remove leading zero if present and add country code for Tunisia (+216)
    String whatsappNumber = cleanPhone;
    if (whatsappNumber.startsWith('0')) {
      whatsappNumber = '216${whatsappNumber.substring(1)}';
    } else if (!whatsappNumber.startsWith('216') &&
        !whatsappNumber.startsWith('+')) {
      whatsappNumber = '216$whatsappNumber';
    }

    final uri = Uri.parse('https://wa.me/$whatsappNumber');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted && !_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('WhatsApp n\'est pas installé sur cet appareil'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  void _showContactOptions(BuildContext context, Client client) {
    if (client.primaryPhone.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Aucun numéro de téléphone disponible'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                SizedBox(height: 20),
                Text(
                  'Contacter ${client.nomComplet}',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                ),
                SizedBox(height: 20),
                ListTile(
                  leading: Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Color(0xFF10B981).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.phone, color: Color(0xFF10B981)),
                  ),
                  title: Text('Appeler'),
                  subtitle: Text(client.primaryPhone),
                  onTap: () {
                    Navigator.pop(context);
                    _makePhoneCall(client.primaryPhone);
                  },
                ),
                ListTile(
                  leading: Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Color(0xFF25D366).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.chat, color: Color(0xFF25D366)),
                  ),
                  title: Text('WhatsApp'),
                  subtitle: Text('Envoyer un message'),
                  onTap: () {
                    Navigator.pop(context);
                    _openWhatsApp(client.primaryPhone);
                  },
                ),
                if (client.email.isNotEmpty)
                  ListTile(
                    leading: Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Color(0xFF3B82F6).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(Icons.email, color: Color(0xFF3B82F6)),
                    ),
                    title: Text('Email'),
                    subtitle: Text(client.email),
                    onTap: () {
                      Navigator.pop(context);
                      _sendEmail(client.email);
                    },
                  ),
                SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  Future<void> _sendEmail(String email) async {
    final uri = Uri.parse('mailto:$email');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted && !_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Impossible d\'ouvrir l\'application email'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          await context.read<FirebaseClientProvider>().refreshClients();
        },
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: isSmallScreen ? 120 : 140,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              elevation: 0,
              scrolledUnderElevation: 2,
              flexibleSpace: FlexibleSpaceBar(
                titlePadding: EdgeInsets.only(left: padding, bottom: 16),
                title: Row(
                  children: [
                    VitaBrosseLogo(
                      height: isSmallScreen ? 24 : 28,
                      showText: false,
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Clients',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 18 : 20,
                          ),
                        ),
                        Text(
                          'Base clients VitaBrosse',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        const Color(0xFF3B82F6).withValues(alpha: 0.05),
                        const Color(0xFF1D4ED8).withValues(alpha: 0.05),
                      ],
                      stops: const [0.0, 0.7, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      Positioned(
                        top: isSmallScreen ? 55 : 50,
                        right: isSmallScreen ? 10 : 15,
                        child: Container(
                          width: isSmallScreen ? 60 : 80,
                          height: isSmallScreen ? 60 : 80,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF3B82F6).withValues(alpha: 0.1),
                                const Color(0xFF1D4ED8).withValues(alpha: 0.1),
                              ],
                            ),
                          ),
                          child: Center(
                            child: Icon(
                              Icons.people_outline,
                              size: isSmallScreen ? 24 : 32,
                              color: const Color(0xFF3B82F6),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.fromLTRB(padding, 16, padding, 8),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(
                      isSmallScreen ? 12 : 16,
                    ),
                    border: Border.all(color: Colors.grey.shade200),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Rechercher un client...',
                      hintStyle: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: isSmallScreen ? 14 : 16,
                      ),
                      prefixIcon: Icon(
                        Icons.search,
                        color: Colors.grey.shade400,
                        size: isSmallScreen ? 20 : 24,
                      ),
                      suffixIcon:
                          _searchController.text.isNotEmpty
                              ? IconButton(
                                icon: Icon(
                                  Icons.clear,
                                  color: Colors.grey.shade400,
                                ),
                                onPressed: () {
                                  _searchController.clear();
                                  context
                                      .read<FirebaseClientProvider>()
                                      .loadClients();
                                },
                              )
                              : null,
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: isSmallScreen ? 16 : 20,
                        vertical: isSmallScreen ? 12 : 16,
                      ),
                    ),
                    onChanged: (value) {
                      if (value.isEmpty) {
                        context.read<FirebaseClientProvider>().loadClients();
                      } else {
                        context.read<FirebaseClientProvider>().searchClients(
                          value,
                        );
                      }
                    },
                  ),
                ),
              ),
            ),
            Consumer<FirebaseClientProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(strokeWidth: 3),
                          SizedBox(height: 16),
                          Text(
                            'Chargement des clients...',
                            style: TextStyle(color: Colors.grey, fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                if (provider.error != null) {
                  return SliverFillRemaining(
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.red.shade50,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Icon(
                                Icons.error_outline,
                                size: 48,
                                color: Colors.red.shade400,
                              ),
                            ),
                            const SizedBox(height: 24),
                            Text(
                              'Oups ! Une erreur est survenue',
                              style: Theme.of(
                                context,
                              ).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF1F2937),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              provider.error!,
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 24),
                            FilledButton.icon(
                              onPressed: () {
                                provider.effacerErreur();
                                provider.chargerClients();
                              },
                              icon: const Icon(Icons.refresh),
                              label: const Text('Réessayer'),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }
                if (provider.clients.isEmpty) {
                  return SliverFillRemaining(
                    child: ModernEmptyState(
                      icon: Icons.people_outline,
                      title: 'Aucun client trouvé',
                      subtitle: 'Commencez par ajouter votre premier client',
                      actionText: 'Ajouter un client',
                      onAction: () => _naviguerVersFormulaire(context),
                    ),
                  );
                }

                // Show the clients list
                return SliverPadding(
                  padding: EdgeInsets.fromLTRB(padding, 8, padding, 20),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      final client = provider.clients[index];
                      return AnimationConfiguration.staggeredList(
                        position: index,
                        duration: const Duration(milliseconds: 375),
                        child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 12),
                              child: _buildModernClientCard(context, client),
                            ),
                          ),
                        ),
                      );
                    }, childCount: provider.clients.length),
                  ),
                );
              },
            ),
          ],
        ),
      ), // RefreshIndicator
      floatingActionButton: FloatingActionButton(
        onPressed: () => _naviguerVersFormulaire(context),
        backgroundColor: const Color(0xFF3B82F6),
        foregroundColor: Colors.white,
        elevation: 6,
        heroTag: "add_client",
        child: const Icon(Icons.add, size: 28),
      ),
    );
  }

  Widget _buildModernClientCard(BuildContext context, Client client) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final isVerySmallScreen = screenWidth < 360;
    final avatarSize = isVerySmallScreen ? 56.0 : (isSmallScreen ? 60.0 : 64.0);
    final titleFontSize =
        isVerySmallScreen ? 15.0 : (isSmallScreen ? 16.0 : 17.0);
    final subtitleFontSize =
        isVerySmallScreen ? 13.0 : (isSmallScreen ? 14.0 : 15.0);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 2, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade100, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 6,
            offset: const Offset(0, 1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onLongPress: () => _showContactOptions(context, client),
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: EdgeInsets.all(isVerySmallScreen ? 16 : 20),
            child: Column(
              children: [
                // Top section with avatar and info
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Enhanced Avatar with status indicator
                    Container(
                      width: avatarSize,
                      height: avatarSize,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFF3B82F6),
                            Color(0xFF1D4ED8),
                            Color(0xFF1E40AF),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          stops: [0.0, 0.6, 1.0],
                        ),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          BoxShadow(
                            color: Color(0xFF3B82F6).withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          _getInitials(client),
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w700,
                            fontSize:
                                isVerySmallScreen
                                    ? 16
                                    : (isSmallScreen ? 18 : 20),
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: isVerySmallScreen ? 16 : 20),
                    // Client information section
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Client name with better typography
                          Text(
                            client.nomComplet,
                            style: TextStyle(
                              fontSize: titleFontSize,
                              fontWeight: FontWeight.w700,
                              color: const Color(0xFF111827),
                              letterSpacing: -0.2,
                              height: 1.2,
                            ),
                            softWrap: true,
                          ),
                          SizedBox(height: 8),
                          // Address with icon and better styling
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                margin: EdgeInsets.only(top: 2),
                                child: Icon(
                                  Icons.location_on_outlined,
                                  size: isVerySmallScreen ? 14 : 16,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                              SizedBox(width: 6),
                              Expanded(
                                child: Text(
                                  client.adresse.isNotEmpty
                                      ? client.adresse
                                      : 'Adresse non renseignée',
                                  style: TextStyle(
                                    fontSize: subtitleFontSize,
                                    color:
                                        client.adresse.isNotEmpty
                                            ? Colors.grey.shade600
                                            : Colors.grey.shade400,
                                    fontWeight: FontWeight.w500,
                                    height: 1.3,
                                    fontStyle:
                                        client.adresse.isEmpty
                                            ? FontStyle.italic
                                            : FontStyle.normal,
                                  ),
                                  softWrap: true,
                                ),
                              ),
                            ],
                          ),
                          // Phone number if available
                          if (client.primaryPhone.isNotEmpty) ...[
                            SizedBox(height: 6),
                            Row(
                              children: [
                                Icon(
                                  Icons.phone_outlined,
                                  size: isVerySmallScreen ? 14 : 16,
                                  color: Color(0xFF10B981),
                                ),
                                SizedBox(width: 6),
                                Text(
                                  client.primaryPhone,
                                  style: TextStyle(
                                    fontSize: subtitleFontSize - 1,
                                    color: Color(0xFF10B981),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
                // Divider
                if (client.primaryPhone.isNotEmpty) ...[
                  SizedBox(height: 16),
                  Container(height: 1, color: Colors.grey.shade100),
                  SizedBox(height: 12),
                ],
                // Bottom action buttons
                Row(
                  children: [
                    if (client.primaryPhone.isNotEmpty) ...[
                      // Call button
                      Expanded(
                        child: _buildBottomActionButton(
                          icon: Icons.phone,
                          label: 'Appeler',
                          color: Color(0xFF10B981),
                          onTap: () => _makePhoneCall(client.primaryPhone),
                          isSmall: isVerySmallScreen,
                        ),
                      ),
                      SizedBox(width: 6),
                      // WhatsApp button
                      Expanded(
                        child: _buildBottomActionButton(
                          icon: Icons.chat_bubble_outline,
                          label: 'WhatsApp',
                          color: Color(0xFF25D366),
                          onTap: () => _openWhatsApp(client.primaryPhone),
                          isSmall: isVerySmallScreen,
                        ),
                      ),
                      SizedBox(width: 6),
                    ],
                    // Detail navigation button
                    Expanded(
                      child: _buildBottomActionButton(
                        icon: Icons.visibility_outlined,
                        label: 'Détails',
                        color: Color(0xFF3B82F6),
                        onTap: () => _naviguerVersDetail(context, client),
                        isSmall: isVerySmallScreen,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    required bool isSmall,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: isSmall ? 8 : 10,
          horizontal: isSmall ? 6 : 8,
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.08),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.15), width: 1),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: isSmall ? 14 : 16, color: color),
            SizedBox(width: 4),
            Flexible(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: isSmall ? 11 : 12,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _naviguerVersFormulaire(BuildContext context, {Client? client}) async {
    await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => ClientFormScreen(client: client)),
    );

    // Just refresh the clients list when returning from the form
    if (mounted && !_isDisposed && context.mounted) {
      context.read<FirebaseClientProvider>().refreshClients();
    }
  }

  void _naviguerVersDetail(BuildContext context, Client client) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ClientDetailScreen(client: client),
      ),
    );

    // Just refresh the clients list when returning from the detail screen
    if (mounted && !_isDisposed && context.mounted) {
      context.read<FirebaseClientProvider>().refreshClients();
    }
  }

  String _getInitials(Client client) {
    // Use nomClient if available
    if (client.nomClient?.isNotEmpty == true) {
      final parts = client.nomClient!.trim().split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0]}${parts[parts.length - 1][0]}'.toUpperCase();
      } else if (parts.length == 1) {
        return parts[0][0].toUpperCase();
      }
    }

    // Fallback to nom/prenom for backward compatibility
    String firstInitial = '';
    if (client.prenom?.isNotEmpty == true) {
      firstInitial = client.prenom![0].toUpperCase();
    }

    String lastInitial = '';
    if (client.nom?.isNotEmpty == true) {
      lastInitial = client.nom![0].toUpperCase();
    }

    final initials = '$firstInitial$lastInitial';
    return initials.isNotEmpty ? initials : '?';
  }
}
