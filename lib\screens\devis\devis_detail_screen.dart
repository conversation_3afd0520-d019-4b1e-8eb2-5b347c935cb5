import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../models/devis.dart';
import '../../models/client.dart';
import '../../providers/devis_provider.dart';
import '../../providers/firebase_client_provider.dart';
import '../../services/devis_service.dart';
import '../../widgets/professional_ui_components.dart';
import '../../widgets/vitabrosse_logo.dart';

class DevisDetailScreen extends StatefulWidget {
  final String devisId;

  const DevisDetailScreen({Key? key, required this.devisId}) : super(key: key);

  @override
  State<DevisDetailScreen> createState() => _DevisDetailScreenState();
}

class _DevisDetailScreenState extends State<DevisDetailScreen> {
  Devis? _devis;
  Client? _client;
  bool _isLoading = true;
  final DevisService _devisService = DevisService();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final devisProvider = Provider.of<DevisProvider>(context, listen: false);
      final clientProvider = Provider.of<FirebaseClientProvider>(
        context,
        listen: false,
      );

      // Charger le devis depuis la liste existante
      final devisList = devisProvider.devis;
      final devis = devisList.firstWhere(
        (d) => d.id == widget.devisId,
        orElse: () => throw Exception('Devis non trouvé'),
      );

      setState(() {
        _devis = devis;
      });

      // Charger le client
      final client = await clientProvider.getClientById(devis.clientId);
      setState(() {
        _client = client;
      });
    } catch (e) {
      print('Erreur lors du chargement des données: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _transformerEnCommande() async {
    if (_devis?.id == null) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      await _devisService.transformerEnCommande(_devis!.id!);

      if (mounted) {
        Navigator.of(context).pop(); // Fermer le loader

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Devis transformé en commande avec succès'),
            backgroundColor: Colors.green,
          ),
        );

        Navigator.of(context).pop(); // Retourner à l'écran précédent
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Fermer le loader

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)} DT';
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _getStatusText(StatutDevis statut) {
    switch (statut) {
      case StatutDevis.brouillon:
        return 'BROUILLON';
      case StatutDevis.envoye:
        return 'ENVOYÉ';
      case StatutDevis.accepte:
        return 'ACCEPTÉ';
      case StatutDevis.refuse:
        return 'REFUSÉ';
      case StatutDevis.expire:
        return 'EXPIRÉ';
    }
  }

  Color _getStatusColor(StatutDevis statut) {
    switch (statut) {
      case StatutDevis.brouillon:
        return Colors.grey;
      case StatutDevis.envoye:
        return Colors.orange;
      case StatutDevis.accepte:
        return Colors.green;
      case StatutDevis.refuse:
        return Colors.red;
      case StatutDevis.expire:
        return Colors.red[700]!;
    }
  }

  Widget _buildCard(Widget child) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(padding: const EdgeInsets.all(16), child: child),
    );
  }

  Widget _buildHeader() {
    if (_devis == null) return const SizedBox.shrink();

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Devis ${_devis!.numero}',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Créé le ${_formatDate(_devis!.dateCreation)}',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor(_devis!.statut),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _getStatusText(_devis!.statut),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          if (_devis!.statut != StatutDevis.accepte &&
              _devis!.statut != StatutDevis.refuse) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _transformerEnCommande,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3498DB),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text('Transformer en commande'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildClientInfo() {
    if (_client == null) return const SizedBox.shrink();

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Informations Client',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow('Nom', _client!.nomComplet),
          _buildInfoRow(
            'Téléphone',
            _client!.primaryPhone.isNotEmpty ? _client!.primaryPhone : 'N/A',
          ),
          _buildInfoRow('Email', _client!.email),
          _buildInfoRow('Adresse', _client!.adresse),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Color(0xFF34495E),
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: TextStyle(color: Colors.grey[700])),
          ),
        ],
      ),
    );
  }

  Widget _buildArticlesTable() {
    if (_devis == null || _devis!.items.isEmpty) {
      return _buildCard(
        const Center(child: Text('Aucun article dans ce devis')),
      );
    }

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Articles',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              headingRowColor: MaterialStateProperty.all(Colors.grey[100]),
              columns: const [
                DataColumn(
                  label: Text(
                    'Article',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Qté',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Prix Unit.',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Total',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
              rows:
                  _devis!.items.map((item) {
                    return DataRow(
                      cells: [
                        DataCell(
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                item.designation,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              if (item.reference.isNotEmpty)
                                Text(
                                  'Réf: ${item.reference}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                            ],
                          ),
                        ),
                        DataCell(Text(item.quantite.toString())),
                        DataCell(Text(_formatCurrency(item.prixUnitaireHT))),
                        DataCell(
                          Text(
                            _formatCurrency(item.sousTotal),
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummary() {
    if (_devis == null) return const SizedBox.shrink();

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Résumé Financier',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 16),
          _buildFinancialRow('Sous-total HT', _devis!.sousTotal),
          _buildFinancialRow(
            'TVA (${_devis!.tauxTva.toStringAsFixed(0)}%)',
            _devis!.montantTva,
          ),
          const Divider(),
          _buildFinancialRow('Total TTC', _devis!.totalTTC, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildFinancialRow(
    String label,
    double amount, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? const Color(0xFF2C3E50) : Colors.grey[700],
            ),
          ),
          Text(
            _formatCurrency(amount),
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color:
                  isTotal ? const Color(0xFF27AE60) : const Color(0xFF2C3E50),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(_devis?.numero ?? 'Détails du devis'),
        backgroundColor: const Color(0xFF2C3E50),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _devis == null
              ? const Center(
                child: Text(
                  'Devis non trouvé',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              )
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildHeader(),
                    _buildClientInfo(),
                    _buildArticlesTable(),
                    _buildFinancialSummary(),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
    );
  }
}
