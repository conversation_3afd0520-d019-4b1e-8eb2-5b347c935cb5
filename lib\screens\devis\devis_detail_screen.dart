import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../models/devis.dart';
import '../../models/client.dart';
import '../../providers/devis_provider.dart';
import '../../providers/firebase_client_provider.dart';
import '../../services/devis_service.dart';
import '../../widgets/professional_ui_components.dart';
import '../../widgets/vitabrosse_logo.dart';
import 'nouveau_devis_screen.dart';

class DevisDetailScreen extends StatefulWidget {
  final String devisId;

  const DevisDetailScreen({Key? key, required this.devisId}) : super(key: key);

  @override
  State<DevisDetailScreen> createState() => _DevisDetailScreenState();
}

class _DevisDetailScreenState extends State<DevisDetailScreen> {
  Devis? _devis;
  Client? _client;
  bool _isLoading = true;
  final DevisService _devisService = DevisService();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final devisProvider = Provider.of<DevisProvider>(context, listen: false);
      final clientProvider = Provider.of<FirebaseClientProvider>(
        context,
        listen: false,
      );

      // Charger le devis depuis la liste existante
      final devisList = devisProvider.devis;
      final devis = devisList.firstWhere(
        (d) => d.id == widget.devisId,
        orElse: () => throw Exception('Devis non trouvé'),
      );

      setState(() {
        _devis = devis;
      });

      // Charger le client
      final client = await clientProvider.getClientById(devis.clientId);
      setState(() {
        _client = client;
      });
    } catch (e) {
      print('Erreur lors du chargement des données: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  bool _isTransforming = false;

  Future<void> _transformerEnCommande() async {
    if (_devis?.id == null || _isTransforming) return;

    setState(() {
      _isTransforming = true;
    });

    try {
      print('DEBUG: Starting transformation using provider...');
      final devisProvider = Provider.of<DevisProvider>(context, listen: false);
      final commandeId = await devisProvider.transformerEnCommande(_devis!.id!);
      
      if (commandeId != null) {
        print('DEBUG: Commande created with ID: $commandeId');
        print('DEBUG: Provider automatically reloaded devis list');

        if (mounted) {
          setState(() {
            _isTransforming = false;
          });

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Devis transformé en commande avec succès (ID: $commandeId)'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );

          // Wait a bit for the snackbar to show, then navigate back
          await Future.delayed(const Duration(milliseconds: 1000));
          
          if (mounted) {
            Navigator.of(context).pop(); // Return to previous screen
          }
        }
      } else {
        throw Exception('Échec de la transformation - ID de commande null');
      }
    } catch (e) {
      print('DEBUG ERROR: Transform failed: $e');
      
      if (mounted) {
        setState(() {
          _isTransforming = false;
        });

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la transformation: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)} DT';
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _getStatusText(StatutDevis statut) {
    switch (statut) {
      case StatutDevis.brouillon:
        return 'BROUILLON';
      case StatutDevis.envoye:
        return 'ENVOYÉ';
      case StatutDevis.accepte:
        return 'ACCEPTÉ';
      case StatutDevis.refuse:
        return 'REFUSÉ';
      case StatutDevis.expire:
        return 'EXPIRÉ';
    }
  }

  Color _getStatusColor(StatutDevis statut) {
    switch (statut) {
      case StatutDevis.brouillon:
        return Colors.grey;
      case StatutDevis.envoye:
        return Colors.orange;
      case StatutDevis.accepte:
        return Colors.green;
      case StatutDevis.refuse:
        return Colors.red;
      case StatutDevis.expire:
        return Colors.red[700]!;
    }
  }

  Widget _buildCard(Widget child) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(padding: const EdgeInsets.all(16), child: child),
    );
  }

  Widget _buildHeader() {
    if (_devis == null) return const SizedBox.shrink();

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Devis ${_devis!.numero}',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Créé le ${_formatDate(_devis!.dateCreation)}',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(_devis!.statut),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      _getStatusText(_devis!.statut),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      // Modifier button
                      Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => _naviguerVersModification(),
                            borderRadius: BorderRadius.circular(8),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: const Color(0xFF059669).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: const Color(0xFF059669).withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: const Icon(
                                Icons.edit,
                                size: 18,
                                color: Color(0xFF059669),
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Supprimer button
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () => _confirmerSuppression(),
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.red.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: Colors.red.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: const Icon(
                              Icons.delete,
                              size: 18,
                              color: Colors.red,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          if (_devis!.statut != StatutDevis.accepte &&
              _devis!.statut != StatutDevis.refuse) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isTransforming ? null : _transformerEnCommande,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isTransforming ? Colors.grey : const Color(0xFF3498DB),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: _isTransforming
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text('Transformation en cours...'),
                        ],
                      )
                    : const Text('Transformer en commande'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildClientInfo() {
    if (_client == null) return const SizedBox.shrink();

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Informations Client',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow('Nom', _client!.nomComplet),
          _buildInfoRow(
            'Téléphone',
            _client!.primaryPhone.isNotEmpty ? _client!.primaryPhone : 'N/A',
          ),
          _buildInfoRow('Email', _client!.email),
          _buildInfoRow('Adresse', _client!.adresse),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Color(0xFF34495E),
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: TextStyle(color: Colors.grey[700])),
          ),
        ],
      ),
    );
  }

  Widget _buildArticlesTable() {
    if (_devis == null || _devis!.items.isEmpty) {
      return _buildCard(
        const Center(child: Text('Aucun article dans ce devis')),
      );
    }

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Articles',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              headingRowColor: MaterialStateProperty.all(Colors.grey[100]),
              columns: const [
                DataColumn(
                  label: Text(
                    'Article',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Qté',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Prix Unit.',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Total',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
              rows:
                  _devis!.items.map((item) {
                    return DataRow(
                      cells: [
                        DataCell(
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                item.designation,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              if (item.reference.isNotEmpty)
                                Text(
                                  'Réf: ${item.reference}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                            ],
                          ),
                        ),
                        DataCell(Text(item.quantite.toString())),
                        DataCell(Text(_formatCurrency(item.prixUnitaireHT))),
                        DataCell(
                          Text(
                            _formatCurrency(item.sousTotal),
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummary() {
    if (_devis == null) return const SizedBox.shrink();

    return _buildCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Résumé Financier',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 16),
          _buildFinancialRow('Sous-total HT', _devis!.sousTotal),
          _buildFinancialRow(
            'TVA (${_devis!.tauxTva.toStringAsFixed(0)}%)',
            _devis!.montantTva,
          ),
          const Divider(),
          _buildFinancialRow('Total TTC', _devis!.totalTTC, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildFinancialRow(
    String label,
    double amount, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? const Color(0xFF2C3E50) : Colors.grey[700],
            ),
          ),
          Text(
            _formatCurrency(amount),
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color:
                  isTotal ? const Color(0xFF27AE60) : const Color(0xFF2C3E50),
            ),
          ),
        ],
      ),
    );
  }

  void _naviguerVersModification() {
    if (_devis != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => NouveauDevisScreen(devisAModifier: _devis),
        ),
      );
    }
  }

  void _confirmerSuppression() {
    if (_devis == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer le devis ${_devis!.numero} ?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context); // Close dialog
              
              try {
                final devisProvider = Provider.of<DevisProvider>(context, listen: false);
                final success = await devisProvider.supprimerDevis(_devis!.id!);
                
                if (success && mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Devis supprimé avec succès'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  Navigator.of(context).pop(); // Return to devis list
                } else if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Erreur lors de la suppression'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(_devis?.numero ?? 'Détails du devis'),
        backgroundColor: const Color(0xFF2C3E50),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _devis == null
              ? const Center(
                child: Text(
                  'Devis non trouvé',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              )
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildHeader(),
                    _buildClientInfo(),
                    _buildArticlesTable(),
                    _buildFinancialSummary(),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
    );
  }
}
