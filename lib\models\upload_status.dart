/// Enum representing the various states of a photo upload operation
enum UploadStatus {
  /// No upload operation in progress
  idle,
  
  /// Upload is currently in progress
  uploading,
  
  /// Upload completed successfully
  success,
  
  /// Upload failed with an error
  failed,
  
  /// Upload is being retried after a failure
  retrying;

  /// Returns true if the upload is in a loading state (uploading or retrying)
  bool get isLoading => this == uploading || this == retrying;

  /// Returns true if the upload has completed (success or failed)
  bool get isCompleted => this == success || this == failed;

  /// Returns true if the upload can be retried
  bool get canRetry => this == failed;

  /// Returns a user-friendly display name for the status
  String get displayName {
    switch (this) {
      case idle:
        return 'En attente';
      case uploading:
        return 'Upload en cours...';
      case success:
        return 'Uploadé avec succès';
      case failed:
        return 'Échec de l\'upload';
      case retrying:
        return 'Nouvel essai...';
    }
  }
}