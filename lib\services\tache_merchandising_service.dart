import '../database/database_helper.dart';
import '../models/tache_merchandising.dart';

class TacheMerchandisingService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Obtenir toutes les tâches
  Future<List<TacheMerchandising>> obtenirToutesLesTaches() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taches_merchandising',
    );
    return List.generate(maps.length, (i) {
      return TacheMerchandising.fromMap(maps[i]);
    });
  }

  // Ajouter une tâche
  Future<int> ajouterTache(TacheMerchandising tache) async {
    final db = await _databaseHelper.database;
    return await db.insert('taches_merchandising', tache.toMap());
  }

  // Modifier une tâche
  Future<bool> modifierTache(TacheMerchandising tache) async {
    final db = await _databaseHelper.database;
    final result = await db.update(
      'taches_merchandising',
      tache.toMap(),
      where: 'id = ?',
      whereArgs: [tache.id],
    );
    return result > 0;
  }

  // Supprimer une tâche
  Future<bool> supprimerTache(int id) async {
    final db = await _databaseHelper.database;
    final result = await db.delete(
      'taches_merchandising',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result > 0;
  }

  // Rechercher des tâches
  Future<List<TacheMerchandising>> rechercherTaches(String terme) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taches_merchandising',
      where: 'titre LIKE ? OR description LIKE ? OR commentaires LIKE ?',
      whereArgs: ['%$terme%', '%$terme%', '%$terme%'],
    );
    return List.generate(maps.length, (i) {
      return TacheMerchandising.fromMap(maps[i]);
    });
  }

  // Obtenir une tâche par ID
  Future<TacheMerchandising?> obtenirTacheParId(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taches_merchandising',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return TacheMerchandising.fromMap(maps.first);
    }
    return null;
  }

  // Obtenir les tâches par merchandiser
  Future<List<TacheMerchandising>> obtenirTachesParMerchandiser(
    int merchandiserId,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taches_merchandising',
      where: 'merchandiserId = ?',
      whereArgs: [merchandiserId],
    );
    return List.generate(maps.length, (i) {
      return TacheMerchandising.fromMap(maps[i]);
    });
  }

  // Obtenir les tâches par date
  Future<List<TacheMerchandising>> obtenirTachesParDate(DateTime date) async {
    final db = await _databaseHelper.database;
    final dateStr = date.toIso8601String().substring(0, 10); // YYYY-MM-DD
    final List<Map<String, dynamic>> maps = await db.query(
      'taches_merchandising',
      where: 'DATE(dateEcheance) = ?',
      whereArgs: [dateStr],
    );
    return List.generate(maps.length, (i) {
      return TacheMerchandising.fromMap(maps[i]);
    });
  }

  // Obtenir les tâches par statut
  Future<List<TacheMerchandising>> obtenirTachesParStatut(String statut) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taches_merchandising',
      where: 'statut = ?',
      whereArgs: [statut],
    );
    return List.generate(maps.length, (i) {
      return TacheMerchandising.fromMap(maps[i]);
    });
  }

  // Obtenir les tâches par type
  Future<List<TacheMerchandising>> obtenirTachesParType(String type) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taches_merchandising',
      where: 'type = ?',
      whereArgs: [type],
    );
    return List.generate(maps.length, (i) {
      return TacheMerchandising.fromMap(maps[i]);
    });
  }

  // Obtenir les tâches par priorité
  Future<List<TacheMerchandising>> obtenirTachesParPriorite(
    String priorite,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taches_merchandising',
      where: 'priorite = ?',
      whereArgs: [priorite],
    );
    return List.generate(maps.length, (i) {
      return TacheMerchandising.fromMap(maps[i]);
    });
  }

  // Obtenir les tâches d'une période
  Future<List<TacheMerchandising>> obtenirTachesPeriode(
    DateTime debut,
    DateTime fin,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'taches_merchandising',
      where: 'dateEcheance BETWEEN ? AND ?',
      whereArgs: [debut.toIso8601String(), fin.toIso8601String()],
    );
    return List.generate(maps.length, (i) {
      return TacheMerchandising.fromMap(maps[i]);
    });
  }

  // Obtenir les tâches d'aujourd'hui
  Future<List<TacheMerchandising>> obtenirTachesAujourdhui() async {
    final aujourdhui = DateTime.now();
    return await obtenirTachesParDate(aujourdhui);
  }

  // Obtenir les tâches de la semaine
  Future<List<TacheMerchandising>> obtenirTachesSemaine() async {
    final maintenant = DateTime.now();
    final debutSemaine = maintenant.subtract(
      Duration(days: maintenant.weekday - 1),
    );
    final finSemaine = debutSemaine.add(const Duration(days: 6));
    return await obtenirTachesPeriode(debutSemaine, finSemaine);
  }

  // Obtenir les tâches du mois
  Future<List<TacheMerchandising>> obtenirTachesMois([DateTime? mois]) async {
    final dateMois = mois ?? DateTime.now();
    final debutMois = DateTime(dateMois.year, dateMois.month, 1);
    final finMois = DateTime(
      dateMois.year,
      dateMois.month + 1,
      1,
    ).subtract(const Duration(days: 1));
    return await obtenirTachesPeriode(debutMois, finMois);
  }

  // Obtenir les tâches en retard
  Future<List<TacheMerchandising>> obtenirTachesEnRetard() async {
    final db = await _databaseHelper.database;
    final maintenant = DateTime.now().toIso8601String();
    final List<Map<String, dynamic>> maps = await db.query(
      'taches_merchandising',
      where: 'dateEcheance < ? AND statut != ?',
      whereArgs: [maintenant, 'terminee'],
    );
    return List.generate(maps.length, (i) {
      return TacheMerchandising.fromMap(maps[i]);
    });
  }

  // Obtenir les tâches avec rappel
  Future<List<TacheMerchandising>> obtenirTachesAvecRappel() async {
    final db = await _databaseHelper.database;
    final maintenant = DateTime.now().toIso8601String();
    final List<Map<String, dynamic>> maps = await db.query(
      'taches_merchandising',
      where: 'rappel = 1 AND dateRappel <= ? AND statut != ?',
      whereArgs: [maintenant, 'terminee'],
    );
    return List.generate(maps.length, (i) {
      return TacheMerchandising.fromMap(maps[i]);
    });
  }

  // Obtenir les statistiques
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    final db = await _databaseHelper.database;

    final nombreTotal = await db.rawQuery(
      'SELECT COUNT(*) as count FROM taches_merchandising',
    );
    final planifiees = await db.rawQuery(
      'SELECT COUNT(*) as count FROM taches_merchandising WHERE statut = ?',
      ['planifiee'],
    );
    final enCours = await db.rawQuery(
      'SELECT COUNT(*) as count FROM taches_merchandising WHERE statut = ?',
      ['en_cours'],
    );
    final terminees = await db.rawQuery(
      'SELECT COUNT(*) as count FROM taches_merchandising WHERE statut = ?',
      ['terminee'],
    );
    final enRetard = await db.rawQuery(
      'SELECT COUNT(*) as count FROM taches_merchandising WHERE dateEcheance < ? AND statut != ?',
      [DateTime.now().toIso8601String(), 'terminee'],
    );

    return {
      'nombreTotal': nombreTotal.first['count'],
      'planifiees': planifiees.first['count'],
      'enCours': enCours.first['count'],
      'terminees': terminees.first['count'],
      'enRetard': enRetard.first['count'],
    };
  }

  // Démarrer une tâche
  Future<bool> demarrerTache(int id) async {
    final tache = await obtenirTacheParId(id);
    if (tache != null && tache.statut == StatutTache.planifiee) {
      final tacheModifiee = tache.copyWith(
        statut: StatutTache.en_cours,
        heureDebut: DateTime.now(),
      );
      return await modifierTache(tacheModifiee);
    }
    return false;
  }

  // Terminer une tâche
  Future<bool> terminerTache(int id, {String? commentaires}) async {
    final tache = await obtenirTacheParId(id);
    if (tache != null && tache.statut == StatutTache.en_cours) {
      final tacheModifiee = tache.copyWith(
        statut: StatutTache.terminee,
        heureFin: DateTime.now(),
        dateRealisation: DateTime.now(),
        commentaires: commentaires ?? tache.commentaires,
      );
      return await modifierTache(tacheModifiee);
    }
    return false;
  }

  // Reporter une tâche
  Future<bool> reporterTache(
    int id,
    DateTime nouvelleDate, {
    String? commentaires,
  }) async {
    final tache = await obtenirTacheParId(id);
    if (tache != null) {
      final tacheModifiee = tache.copyWith(
        statut: StatutTache.reportee,
        dateEcheance: nouvelleDate,
        commentaires: commentaires ?? tache.commentaires,
      );
      return await modifierTache(tacheModifiee);
    }
    return false;
  }

  // Annuler une tâche
  Future<bool> annulerTache(int id, {String? commentaires}) async {
    final tache = await obtenirTacheParId(id);
    if (tache != null) {
      final tacheModifiee = tache.copyWith(
        statut: StatutTache.annulee,
        commentaires: commentaires ?? tache.commentaires,
      );
      return await modifierTache(tacheModifiee);
    }
    return false;
  }
}
