import 'package:flutter_test/flutter_test.dart';
import 'package:vitabrosse_pro/models/upload_status.dart';

void main() {
  group('UploadStatus', () {
    test('should have correct isLoading property', () {
      expect(UploadStatus.idle.isLoading, isFalse);
      expect(UploadStatus.uploading.isLoading, isTrue);
      expect(UploadStatus.success.isLoading, isFalse);
      expect(UploadStatus.failed.isLoading, isFalse);
      expect(UploadStatus.retrying.isLoading, isTrue);
    });

    test('should have correct isCompleted property', () {
      expect(UploadStatus.idle.isCompleted, isFalse);
      expect(UploadStatus.uploading.isCompleted, isFalse);
      expect(UploadStatus.success.isCompleted, isTrue);
      expect(UploadStatus.failed.isCompleted, isTrue);
      expect(UploadStatus.retrying.isCompleted, isFalse);
    });

    test('should have correct canRetry property', () {
      expect(UploadStatus.idle.canRetry, isFalse);
      expect(UploadStatus.uploading.canRetry, isFalse);
      expect(UploadStatus.success.canRetry, isFalse);
      expect(UploadStatus.failed.canRetry, isTrue);
      expect(UploadStatus.retrying.canRetry, isFalse);
    });

    test('should have French display names', () {
      expect(UploadStatus.idle.displayName, equals('En attente'));
      expect(UploadStatus.uploading.displayName, equals('Upload en cours...'));
      expect(UploadStatus.success.displayName, equals('Uploadé avec succès'));
      expect(UploadStatus.failed.displayName, equals('Échec de l\'upload'));
      expect(UploadStatus.retrying.displayName, equals('Nouvel essai...'));
    });
  });
}