import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'commercial.db');
    return await openDatabase(
      path,
      version: 4,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Table des clients
    await db.execute('''
      CREATE TABLE clients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        telephone TEXT NOT NULL,
        adresse TEXT NOT NULL,
        dateCreation TEXT NOT NULL,
        codeClient TEXT,
        matriculeFiscal TEXT,
        categorie TEXT,
        modeReglement TEXT
      )
    ''');

    // Table des produits
    await db.execute('''
      CREATE TABLE produits (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        description TEXT NOT NULL,
        prix REAL NOT NULL,
        stock INTEGER NOT NULL,
        imageUrl TEXT,
        categorie TEXT NOT NULL,
        dateCreation TEXT NOT NULL,
        actif INTEGER NOT NULL DEFAULT 1
      )
    ''');

    // Table des commandes
    await db.execute('''
      CREATE TABLE commandes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        clientId INTEGER NOT NULL,
        dateCommande TEXT NOT NULL,
        statut INTEGER NOT NULL,
        montantTotal REAL NOT NULL,
        notes TEXT,
        FOREIGN KEY (clientId) REFERENCES clients (id)
      )
    ''');

    // Table des items de commande
    await db.execute('''
      CREATE TABLE commande_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        commandeId INTEGER NOT NULL,
        produitId INTEGER NOT NULL,
        nomProduit TEXT NOT NULL,
        prixUnitaire REAL NOT NULL,
        quantite INTEGER NOT NULL,
        sousTotal REAL NOT NULL,
        FOREIGN KEY (commandeId) REFERENCES commandes (id),
        FOREIGN KEY (produitId) REFERENCES produits (id)
      )
    ''');

    // Table des merchandisers
    await db.execute('''
      CREATE TABLE merchandisers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        telephone TEXT NOT NULL,
        zone TEXT NOT NULL,
        actif INTEGER NOT NULL DEFAULT 1,
        dateCreation TEXT NOT NULL
      )
    ''');

    // Table des magasins
    await db.execute('''
      CREATE TABLE magasins (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        adresse TEXT NOT NULL,
        ville TEXT NOT NULL,
        codePostal TEXT NOT NULL,
        telephone TEXT NOT NULL,
        email TEXT,
        typeCommerce TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        actif INTEGER NOT NULL DEFAULT 1,
        dateCreation TEXT NOT NULL
      )
    ''');

    // Table des parcours
    await db.execute('''
      CREATE TABLE parcours (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        description TEXT NOT NULL,
        merchandiserId INTEGER NOT NULL,
        dateVisite TEXT NOT NULL,
        statut TEXT NOT NULL DEFAULT 'planifie',
        clientIds TEXT NOT NULL,
        dateCreation TEXT NOT NULL,
        dateDebut TEXT,
        dateFin TEXT,
        commentaires TEXT,
        FOREIGN KEY (merchandiserId) REFERENCES merchandisers (id)
      )
    ''');

    // Table des visites
    await db.execute('''
      CREATE TABLE visites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        parcoursId INTEGER NOT NULL,
        clientId TEXT NOT NULL,
        dateVisite TEXT NOT NULL,
        heureArrivee TEXT,
        heureDepart TEXT,
        statut TEXT NOT NULL DEFAULT 'prevue',
        commentaires TEXT,
        photos TEXT,
        donneesVisite TEXT,
        dateCreation TEXT NOT NULL,
        FOREIGN KEY (parcoursId) REFERENCES parcours (id)
      )
    ''');

    // Table des tâches de merchandising
    await db.execute('''
      CREATE TABLE taches_merchandising (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        titre TEXT NOT NULL,
        description TEXT NOT NULL,
        type TEXT NOT NULL,
        statut TEXT NOT NULL DEFAULT 'planifiee',
        priorite TEXT NOT NULL DEFAULT 'normale',
        merchandiserId INTEGER NOT NULL,
        clientId TEXT,
        dateEcheance TEXT NOT NULL,
        heureDebut TEXT,
        heureFin TEXT,
        dureeEstimee INTEGER,
        commentaires TEXT,
        photos TEXT,
        donnees TEXT,
        dateCreation TEXT NOT NULL,
        dateRealisation TEXT,
        rappel INTEGER NOT NULL DEFAULT 0,
        dateRappel TEXT,
        FOREIGN KEY (merchandiserId) REFERENCES merchandisers (id)
      )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Ajouter les nouvelles tables pour la version 2
      await db.execute('''
        CREATE TABLE merchandisers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          nom TEXT NOT NULL,
          prenom TEXT NOT NULL,
          email TEXT NOT NULL UNIQUE,
          telephone TEXT NOT NULL,
          zone TEXT NOT NULL,
          actif INTEGER NOT NULL DEFAULT 1,
          dateCreation TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE magasins (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          nom TEXT NOT NULL,
          adresse TEXT NOT NULL,
          ville TEXT NOT NULL,
          codePostal TEXT NOT NULL,
          telephone TEXT NOT NULL,
          email TEXT,
          typeCommerce TEXT NOT NULL,
          latitude REAL NOT NULL,
          longitude REAL NOT NULL,
          actif INTEGER NOT NULL DEFAULT 1,
          dateCreation TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE parcours (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          nom TEXT NOT NULL,
          description TEXT NOT NULL,
          merchandiserId INTEGER NOT NULL,
          dateVisite TEXT NOT NULL,
          statut TEXT NOT NULL DEFAULT 'planifie',
          clientIds TEXT NOT NULL,
          dateCreation TEXT NOT NULL,
          dateDebut TEXT,
          dateFin TEXT,
          commentaires TEXT,
          FOREIGN KEY (merchandiserId) REFERENCES merchandisers (id)
        )
      ''');

      await db.execute('''
        CREATE TABLE visites (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          parcoursId INTEGER NOT NULL,
          magasinId INTEGER NOT NULL,
          dateVisite TEXT NOT NULL,
          heureArrivee TEXT,
          heureDepart TEXT,
          statut TEXT NOT NULL DEFAULT 'prevue',
          commentaires TEXT,
          photos TEXT,
          donneesVisite TEXT,
          dateCreation TEXT NOT NULL,
          FOREIGN KEY (parcoursId) REFERENCES parcours (id),
          FOREIGN KEY (magasinId) REFERENCES magasins (id)
        )
      ''');
    }

    if (oldVersion < 3) {
      // Ajouter les nouveaux champs pour les clients dans la version 3
      await db.execute('ALTER TABLE clients ADD COLUMN codeClient TEXT');
      await db.execute('ALTER TABLE clients ADD COLUMN matriculeFiscal TEXT');
      await db.execute('ALTER TABLE clients ADD COLUMN categorie TEXT');
      await db.execute('ALTER TABLE clients ADD COLUMN modeReglement TEXT');
    }

    if (oldVersion < 4) {
      // Ajouter la table des tâches de merchandising dans la version 4
      await db.execute('''
        CREATE TABLE taches_merchandising (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          titre TEXT NOT NULL,
          description TEXT NOT NULL,
          type TEXT NOT NULL,
          statut TEXT NOT NULL DEFAULT 'planifiee',
          priorite TEXT NOT NULL DEFAULT 'normale',
          merchandiserId INTEGER NOT NULL,
          magasinId INTEGER,
          dateEcheance TEXT NOT NULL,
          heureDebut TEXT,
          heureFin TEXT,
          dureeEstimee INTEGER,
          commentaires TEXT,
          photos TEXT,
          donnees TEXT,
          dateCreation TEXT NOT NULL,
          dateRealisation TEXT,
          rappel INTEGER NOT NULL DEFAULT 0,
          dateRappel TEXT,
          FOREIGN KEY (merchandiserId) REFERENCES merchandisers (id),
          FOREIGN KEY (magasinId) REFERENCES magasins (id)
        )
      ''');
    }
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), 'commercial.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
