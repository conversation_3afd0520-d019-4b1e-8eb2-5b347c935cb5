import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../models/devis.dart';
import '../../widgets/vitabrosse_logo.dart';
import '../../widgets/professional_ui_components.dart';
import '../../providers/devis_provider.dart';
import '../../screens/devis/nouveau_devis_screen.dart';
import '../../screens/devis/devis_detail_screen.dart';

class DevisScreen extends StatefulWidget {
  const DevisScreen({super.key});

  @override
  State<DevisScreen> createState() => _DevisScreenState();
}

class _DevisScreenState extends State<DevisScreen> {
  final TextEditingController _rechercheController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<DevisProvider>().chargerDevis();
    });
  }

  @override
  void dispose() {
    _rechercheController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      floatingActionButton: FloatingActionButton(
        onPressed: () => _naviguerVersNouveauDevis(),
        backgroundColor: const Color(0xFF3B82F6),
        foregroundColor: Colors.white,
        tooltip: 'Nouveau devis',
        child: const Icon(Icons.add),
      ),
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 1,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(left: padding, bottom: 16),
              title: Row(
                children: [
                  const VitaBrosseLogo(height: 28, showText: false),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Devis',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 18 : 22,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'Commerciales',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF3B82F6).withValues(alpha: 0.05),
                      const Color(0xFF1D4ED8).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: isSmallScreen ? 55 : 30,
                      right: isSmallScreen ? 10 : 15,
                      child: Container(
                        width: isSmallScreen ? 60 : 80,
                        height: isSmallScreen ? 60 : 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF3B82F6).withValues(alpha: 0.1),
                              const Color(0xFF1D4ED8).withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.description_outlined,
                            size: isSmallScreen ? 24 : 32,
                            color: const Color(0xFF3B82F6),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.fromLTRB(padding, 16, padding, 8),
              child: Column(
                children: [
                  // Champ de recherche optimisé avec design moderne
                  ProfessionalCard(
                    padding: EdgeInsets.zero,
                    margin: EdgeInsets.zero,
                    child: TextField(
                      controller: _rechercheController,
                      style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
                      decoration: InputDecoration(
                        hintText: 'Rechercher un devis par numéro ou client...',
                        hintStyle: TextStyle(
                          color: Colors.grey[500],
                          fontSize: isSmallScreen ? 14 : 16,
                        ),
                        prefixIcon: Icon(
                          Icons.search,
                          color: Colors.grey[500],
                          size: isSmallScreen ? 20 : 24,
                        ),
                        suffixIcon:
                            _rechercheController.text.isNotEmpty
                                ? IconButton(
                                  icon: Icon(
                                    Icons.clear,
                                    size: isSmallScreen ? 18 : 20,
                                  ),
                                  onPressed: () {
                                    _rechercheController.clear();
                                    setState(() {});
                                  },
                                )
                                : null,
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 12 : 16,
                          vertical: isSmallScreen ? 16 : 18,
                        ),
                      ),
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Liste des devis
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: padding),
            sliver: Consumer<DevisProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            color: const Color(0xFF3B82F6),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Chargement des devis...',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                if (provider.error != null) {
                  return SliverFillRemaining(
                    child: Center(
                      child: ProfessionalCard(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 64,
                              color: Colors.red[300],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Erreur',
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(fontWeight: FontWeight.w600),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              provider.error!,
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                            const SizedBox(height: 16),
                            PrimaryActionButton(
                              text: 'Réessayer',
                              icon: Icons.refresh,
                              onPressed: () => provider.chargerDevis(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }

                List<Devis> devisFiltres = provider.devis;

                // Appliquer le filtre de recherche
                if (_rechercheController.text.isNotEmpty) {
                  final terme = _rechercheController.text.toLowerCase();
                  devisFiltres =
                      devisFiltres
                          .where(
                            (devis) =>
                                devis.numero.toLowerCase().contains(terme) ||
                                devis.clientId.toLowerCase().contains(terme),
                          )
                          .toList();
                }

                if (devisFiltres.isEmpty) {
                  return SliverFillRemaining(
                    child: Center(
                      child: ProfessionalCard(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.description_outlined,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Aucun devis trouvé',
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(fontWeight: FontWeight.w600),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _rechercheController.text.isNotEmpty
                                  ? 'Aucun devis ne correspond à vos critères'
                                  : 'Créez votre premier devis',
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }

                return SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final devis = devisFiltres[index];
                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildDevisCard(devis, isSmallScreen),
                        ),
                      ),
                    );
                  }, childCount: devisFiltres.length),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDevisCard(Devis devis, bool isSmallScreen) {
    return ProfessionalCard(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 12 : 16),
      onTap: () => _naviguerVersDetail(devis),
      hasShadow: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header avec numéro et statut
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      devis.numeroFormate,
                      style: TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: isSmallScreen ? 16 : 18,
                        color: const Color(0xFF1F2937),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Client: ${devis.clientId}',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 12 : 14,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      onSelected: (value) => _gererActionDevis(value, devis),
                      icon: Icon(
                        Icons.more_vert,
                        size: isSmallScreen ? 18 : 20,
                        color: Colors.grey[600],
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      itemBuilder:
                          (context) => [
                            const PopupMenuItem(
                              value: 'voir',
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.visibility,
                                    size: 18,
                                    color: Color(0xFF3B82F6),
                                  ),
                                  SizedBox(width: 12),
                                  Text('Voir les détails'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'modifier',
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.edit,
                                    size: 18,
                                    color: Color(0xFF059669),
                                  ),
                                  SizedBox(width: 12),
                                  Text('Modifier'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'dupliquer',
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.copy,
                                    size: 18,
                                    color: Color(0xFF7C3AED),
                                  ),
                                  SizedBox(width: 12),
                                  Text('Dupliquer'),
                                ],
                              ),
                            ),
                            const PopupMenuDivider(),
                            const PopupMenuItem(
                              value: 'supprimer',
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                    size: 18,
                                  ),
                                  SizedBox(width: 12),
                                  Text(
                                    'Supprimer',
                                    style: TextStyle(color: Colors.red),
                                  ),
                                ],
                              ),
                            ),
                          ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: isSmallScreen ? 12 : 16),

          // Informations du devis avec icônes colorées
          Container(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        Icons.calendar_today,
                        size: isSmallScreen ? 14 : 16,
                        color: const Color(0xFF3B82F6),
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Créé le ${DateFormat('dd/MM/yyyy').format(devis.dateCreation)}',
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: isSmallScreen ? 12 : 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: isSmallScreen ? 8 : 10),
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color:
                            devis.estExpire
                                ? Colors.red.withValues(alpha: 0.1)
                                : const Color(
                                  0xFF059669,
                                ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        devis.estExpire ? Icons.warning : Icons.access_time,
                        size: isSmallScreen ? 14 : 16,
                        color:
                            devis.estExpire
                                ? Colors.red
                                : const Color(0xFF059669),
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Expire le ${DateFormat('dd/MM/yyyy').format(devis.dateExpiration)}',
                        style: TextStyle(
                          color:
                              devis.estExpire ? Colors.red : Colors.grey[700],
                          fontWeight:
                              devis.estExpire
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                          fontSize: isSmallScreen ? 12 : 14,
                        ),
                      ),
                    ),
                    if (devis.estExpire)
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.red.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          'EXPIRÉ',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 10,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: isSmallScreen ? 12 : 16),

          // Footer avec articles et montant
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 8 : 12,
                  vertical: isSmallScreen ? 6 : 8,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF7C3AED).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.shopping_cart_outlined,
                      size: isSmallScreen ? 14 : 16,
                      color: const Color(0xFF7C3AED),
                    ),
                    SizedBox(width: 4),
                    Text(
                      '${devis.nombreArticles} article(s)',
                      style: TextStyle(
                        color: const Color(0xFF7C3AED),
                        fontSize: isSmallScreen ? 12 : 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    devis.totalTTCFormate,
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF059669),
                      fontSize: isSmallScreen ? 18 : 20,
                    ),
                  ),
                  Text(
                    'TTC',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: isSmallScreen ? 10 : 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _naviguerVersNouveauDevis() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NouveauDevisScreen()),
    );
  }

  void _naviguerVersModification(Devis devis) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NouveauDevisScreen(devisAModifier: devis),
      ),
    );
  }

  void _naviguerVersDetail(Devis devis) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DevisDetailScreen(devisId: devis.id!),
      ),
    );
  }

  void _gererActionDevis(String action, Devis devis) {
    switch (action) {
      case 'voir':
        _naviguerVersDetail(devis);
        break;
      case 'modifier':
        _naviguerVersModification(devis);
        break;
      case 'dupliquer':
        _dupliquerDevis(devis);
        break;
      case 'supprimer':
        _confirmerSuppression(devis);
        break;
    }
  }

  void _dupliquerDevis(Devis devis) async {
    final nouveauDevisId = await context.read<DevisProvider>().dupliquerDevis(
      devis.id!,
    );

    if (nouveauDevisId != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Devis dupliqué avec succès')),
      );
    }
  }

  void _confirmerSuppression(Devis devis) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la suppression'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer le devis ${devis.numeroFormate} ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  final success = await context
                      .read<DevisProvider>()
                      .supprimerDevis(devis.id!);
                  if (success && mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Devis supprimé avec succès'),
                      ),
                    );
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }
}
