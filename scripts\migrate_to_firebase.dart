import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import '../lib/firebase_options.dart';

/// Script de migration des données SQLite vers Firestore
///
/// Usage:
/// dart run scripts/migrate_to_firebase.dart
class FirebaseMigration {
  static FirebaseFirestore? _firestore;

  static Future<void> initialize() async {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    _firestore = FirebaseFirestore.instance;
  }

  /// Migrer les clients depuis un fichier JSON
  static Future<void> migrateClients(String jsonFilePath) async {
    try {
      final file = File(jsonFilePath);
      if (!file.existsSync()) {
        print('Fichier clients non trouvé: $jsonFilePath');
        return;
      }

      final jsonString = await file.readAsString();
      final List<dynamic> clientsData = json.decode(jsonString);

      print('Migration de ${clientsData.length} clients...');

      for (final clientData in clientsData) {
        final Map<String, dynamic> data = {
          'nom': clientData['nom'],
          'prenom': clientData['prenom'],
          'email': clientData['email'],
          'telephone': clientData['telephone'],
          'adresse': clientData['adresse'],
          'dateCreation': Timestamp.fromDate(
            DateTime.parse(clientData['dateCreation']),
          ),
          'codeClient': clientData['codeClient'],
          'matriculeFiscal': clientData['matriculeFiscal'],
          'categorie': clientData['categorie'],
          'modeReglement': clientData['modeReglement'],
        };

        await _firestore!.collection('clients').add(data);
        print('Client migré: ${clientData['nom']} ${clientData['prenom']}');
      }

      print('Migration des clients terminée !');
    } catch (e) {
      print('Erreur lors de la migration des clients: $e');
    }
  }

  /// Migrer les produits depuis un fichier JSON
  static Future<void> migrateProduits(String jsonFilePath) async {
    try {
      final file = File(jsonFilePath);
      if (!file.existsSync()) {
        print('Fichier produits non trouvé: $jsonFilePath');
        return;
      }

      final jsonString = await file.readAsString();
      final List<dynamic> produitsData = json.decode(jsonString);

      print('Migration de ${produitsData.length} produits...');

      for (final produitData in produitsData) {
        final Map<String, dynamic> data = {
          'nom': produitData['nom'],
          'description': produitData['description'],
          'prix': produitData['prix'],
          'stock': produitData['stock'],
          'code': produitData['code'],
          'categorie': produitData['categorie'],
          'marque': produitData['marque'],
          'taille': produitData['taille'],
          'couleur': produitData['couleur'],
          'imageUrl': produitData['imageUrl'],
          'dateCreation': Timestamp.fromDate(
            DateTime.parse(produitData['dateCreation']),
          ),
        };

        await _firestore!.collection('produits').add(data);
        print('Produit migré: ${produitData['nom']}');
      }

      print('Migration des produits terminée !');
    } catch (e) {
      print('Erreur lors de la migration des produits: $e');
    }
  }

  /// Migrer les commandes depuis un fichier JSON
  static Future<void> migrateCommandes(String jsonFilePath) async {
    try {
      final file = File(jsonFilePath);
      if (!file.existsSync()) {
        print('Fichier commandes non trouvé: $jsonFilePath');
        return;
      }

      final jsonString = await file.readAsString();
      final List<dynamic> commandesData = json.decode(jsonString);

      print('Migration de ${commandesData.length} commandes...');

      for (final commandeData in commandesData) {
        final Map<String, dynamic> data = {
          'clientId': commandeData['clientId'],
          'numero': commandeData['numero'],
          'statut': commandeData['statut'],
          'total': commandeData['total'],
          'dateCommande': Timestamp.fromDate(
            DateTime.parse(commandeData['dateCommande']),
          ),
          'dateLivraison':
              commandeData['dateLivraison'] != null
                  ? Timestamp.fromDate(
                    DateTime.parse(commandeData['dateLivraison']),
                  )
                  : null,
          'items': commandeData['items'],
          'notes': commandeData['notes'],
        };

        await _firestore!.collection('commandes').add(data);
        print('Commande migrée: ${commandeData['numero']}');
      }

      print('Migration des commandes terminée !');
    } catch (e) {
      print('Erreur lors de la migration des commandes: $e');
    }
  }

  /// Exporter les données SQLite vers JSON
  static Future<void> exportSQLiteToJSON() async {
    // TODO: Implémenter l'export depuis SQLite
    print('Export SQLite vers JSON à implémenter...');
  }

  /// Nettoyer les données de test
  static Future<void> cleanTestData() async {
    print('Nettoyage des données de test...');

    final collections = ['clients', 'produits', 'commandes', 'devis'];

    for (final collection in collections) {
      final snapshot = await _firestore!.collection(collection).get();
      for (final doc in snapshot.docs) {
        await doc.reference.delete();
      }
      print('Collection $collection nettoyée');
    }

    print('Nettoyage terminé !');
  }
}

/// Point d'entrée principal
void main(List<String> args) async {
  print('=== Migration Firebase VitaBrosse Pro ===');

  if (args.isEmpty) {
    print('Usage:');
    print(
      '  dart run scripts/migrate_to_firebase.dart clients data/clients.json',
    );
    print(
      '  dart run scripts/migrate_to_firebase.dart produits data/produits.json',
    );
    print(
      '  dart run scripts/migrate_to_firebase.dart commandes data/commandes.json',
    );
    print('  dart run scripts/migrate_to_firebase.dart clean');
    return;
  }

  await FirebaseMigration.initialize();

  final command = args[0];

  switch (command) {
    case 'clients':
      if (args.length < 2) {
        print('Erreur: Chemin du fichier JSON requis');
        return;
      }
      await FirebaseMigration.migrateClients(args[1]);
      break;

    case 'produits':
      if (args.length < 2) {
        print('Erreur: Chemin du fichier JSON requis');
        return;
      }
      await FirebaseMigration.migrateProduits(args[1]);
      break;

    case 'commandes':
      if (args.length < 2) {
        print('Erreur: Chemin du fichier JSON requis');
        return;
      }
      await FirebaseMigration.migrateCommandes(args[1]);
      break;

    case 'clean':
      await FirebaseMigration.cleanTestData();
      break;

    default:
      print('Commande inconnue: $command');
  }

  print('Migration terminée !');
}
