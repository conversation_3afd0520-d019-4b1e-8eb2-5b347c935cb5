import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../models/client.dart';
import '../../providers/firebase_client_provider.dart';
import '../../widgets/professional_ui_components.dart';
import 'client_form_screen.dart';

class ClientDetailScreen extends StatefulWidget {
  final Client client;

  const ClientDetailScreen({super.key, required this.client});

  @override
  State<ClientDetailScreen> createState() => _ClientDetailScreenState();
}

class _ClientDetailScreenState extends State<ClientDetailScreen> {
  late Client _currentClient;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _currentClient = widget.client;
    // Orders can be loaded later when implemented
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  Future<void> _deleteClient() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Supprimer le client'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer ${_currentClient.nomComplet} ? Cette action est irréversible.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Annuler'),
              ),
              FilledButton(
                onPressed: () => Navigator.pop(context, true),
                style: FilledButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );

    if (confirm == true && mounted && !_isDisposed) {
      final provider = context.read<FirebaseClientProvider>();
      final success = await provider.supprimerClient(_currentClient.id!);

      if (success && mounted && !_isDisposed) {
        // Show success message in the detail screen itself
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Client supprimé avec succès'),
            backgroundColor: Color(0xFF10B981),
          ),
        );

        // Small delay to show the message, then navigate back
        await Future.delayed(const Duration(milliseconds: 500));

        if (mounted && !_isDisposed) {
          Navigator.pop(context, {
            'success': true,
            'message': 'Client supprimé avec succès',
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Color(0xFF1F2937)),
              onPressed: () => Navigator.pop(context),
            ),
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(
                left: 56, // Space for back button
                bottom: 16,
                right: 100, // Space for popup menu
              ),
              title: Row(
                children: [
                  Container(
                    width: isSmallScreen ? 24 : 28,
                    height: isSmallScreen ? 24 : 28,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Center(
                      child: Text(
                        _getInitials(_currentClient),
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: isSmallScreen ? 12 : 14,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _currentClient.nomComplet,
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 16 : 18,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'Détails du client',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF3B82F6).withValues(alpha: 0.05),
                      const Color(0xFF1D4ED8).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: isSmallScreen ? 35 : 50,
                      right: isSmallScreen ? 10 : 15,
                      child: Container(
                        width: isSmallScreen ? 60 : 80,
                        height: isSmallScreen ? 60 : 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF3B82F6).withValues(alpha: 0.1),
                              const Color(0xFF1D4ED8).withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.person_outline,
                            size: isSmallScreen ? 24 : 32,
                            color: const Color(0xFF3B82F6),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _naviguerVersModification();
                      break;
                    case 'delete':
                      _deleteClient();
                      break;
                  }
                },
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 20),
                            SizedBox(width: 12),
                            Text('Modifier'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 20, color: Colors.red),
                            SizedBox(width: 12),
                            Text(
                              'Supprimer',
                              style: TextStyle(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ],
              ),
            ],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(padding),
              child: Column(
                children: [
                  // Personal Information
                  _buildPersonalInfoCard(isSmallScreen, padding),
                  const SizedBox(height: 20),
                  // Business Information
                  _buildBusinessInfoCard(isSmallScreen, padding),
                  const SizedBox(height: 20),
                  // Client Statistics
                  _buildStatsCard(isSmallScreen, padding),
                  const SizedBox(height: 100), // Space for FAB
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _naviguerVersModification,
        backgroundColor: const Color(0xFF3B82F6),
        foregroundColor: Colors.white,
        elevation: 6,
        heroTag: "edit_client",
        icon: const Icon(Icons.edit),
        label: const Text('Modifier'),
      ),
    );
  }

  Widget _buildPersonalInfoCard(bool isSmallScreen, double padding) {
    return ProfessionalCard(
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations personnelles',
              style: TextStyle(
                fontSize: isSmallScreen ? 18 : 20,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              Icons.person,
              'Nom complet',
              _currentClient.nomComplet,
            ),
            _buildInfoRow(Icons.email, 'Email', _currentClient.email),
            _buildInfoRow(
              Icons.phone,
              'Téléphone',
              _currentClient.primaryPhone,
            ),
            _buildInfoRow(Icons.location_on, 'Adresse', _currentClient.adresse),
            _buildInfoRow(
              Icons.calendar_today,
              'Client depuis',
              DateFormat(
                'dd/MM/yyyy',
              ).format(_currentClient.primaryCreationDate),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBusinessInfoCard(bool isSmallScreen, double padding) {
    final hasBusinessInfo =
        _currentClient.codeClient?.isNotEmpty == true ||
        _currentClient.matriculeFiscale?.isNotEmpty == true ||
        _currentClient.categorie?.isNotEmpty == true ||
        _currentClient.modeReglement?.isNotEmpty == true;

    if (!hasBusinessInfo) return const SizedBox.shrink();

    return ProfessionalCard(
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations commerciales',
              style: TextStyle(
                fontSize: isSmallScreen ? 18 : 20,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 16),
            if (_currentClient.codeClient?.isNotEmpty == true)
              _buildInfoRow(
                Icons.qr_code,
                'Code Client',
                _currentClient.codeClient!,
              ),
            if (_currentClient.matriculeFiscale?.isNotEmpty == true)
              _buildInfoRow(
                Icons.business,
                'Matricule Fiscal',
                _currentClient.matriculeFiscale!,
              ),
            if (_currentClient.categorie?.isNotEmpty == true)
              _buildInfoRow(
                Icons.category,
                'Catégorie',
                _currentClient.categorie!,
              ),
            if (_currentClient.modeReglement?.isNotEmpty == true)
              _buildInfoRow(
                Icons.payment,
                'Mode de Règlement',
                _currentClient.modeReglement!,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard(bool isSmallScreen, double padding) {
    return ProfessionalCard(
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistiques',
              style: TextStyle(
                fontSize: isSmallScreen ? 18 : 20,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.shopping_bag,
                    value: '0', // TODO: Replace with actual order count
                    label: 'Commandes',
                    color: const Color(0xFF3B82F6),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.attach_money,
                    value: '0 DT', // TODO: Replace with actual total
                    label: 'Total dépensé',
                    color: const Color(0xFF10B981),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    if (value.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, size: 20, color: const Color(0xFF3B82F6)),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _naviguerVersModification() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ClientFormScreen(client: _currentClient),
      ),
    );

    // Show success message if returned from form
    if (result != null && result['success'] == true && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['message']),
          backgroundColor: const Color(0xFF10B981),
        ),
      );

      // Update the current client if we received an updated one
      if (result['updatedClient'] != null) {
        setState(() {
          _currentClient = result['updatedClient'];
        });
      }
    }
  }

  String _getInitials(Client client) {
    if (client.nomClient?.isNotEmpty == true) {
      final parts = client.nomClient!.trim().split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0]}${parts[parts.length - 1][0]}'.toUpperCase();
      } else if (parts.length == 1) {
        return parts[0][0].toUpperCase();
      }
    }

    String firstInitial = '';
    if (client.prenom?.isNotEmpty == true) {
      firstInitial = client.prenom![0].toUpperCase();
    }

    String lastInitial = '';
    if (client.nom?.isNotEmpty == true) {
      lastInitial = client.nom![0].toUpperCase();
    }

    final initials = '$firstInitial$lastInitial';
    return initials.isNotEmpty ? initials : '?';
  }
}
