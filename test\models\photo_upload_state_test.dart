import 'package:flutter_test/flutter_test.dart';
import 'package:vitabrosse_pro/models/photo_upload_state.dart';
import 'package:vitabrosse_pro/models/upload_status.dart';

void main() {
  group('PhotoUploadState', () {
    const testPath = '/test/path/image.jpg';
    const testDriveUrl = 'https://drive.google.com/file/d/test123/view';
    const testError = 'Upload failed';

    test('should create idle state with factory constructor', () {
      final state = PhotoUploadState.idle(testPath);

      expect(state.localPath, equals(testPath));
      expect(state.status, equals(UploadStatus.idle));
      expect(state.driveUrl, isNull);
      expect(state.error, isNull);
      expect(state.progress, isNull);
      expect(state.retryCount, equals(0));
    });

    test('should create uploading state', () {
      final initialState = PhotoUploadState.idle(testPath);
      final uploadingState = initialState.uploading(progress: 0.5);

      expect(uploadingState.localPath, equals(testPath));
      expect(uploadingState.status, equals(UploadStatus.uploading));
      expect(uploadingState.progress, equals(0.5));
      expect(uploadingState.retryCount, equals(0));
    });

    test('should create success state', () {
      final initialState = PhotoUploadState.idle(testPath);
      final successState = initialState.success(testDriveUrl);

      expect(successState.localPath, equals(testPath));
      expect(successState.status, equals(UploadStatus.success));
      expect(successState.driveUrl, equals(testDriveUrl));
      expect(successState.isUploaded, isTrue);
    });

    test('should create failed state', () {
      final initialState = PhotoUploadState.idle(testPath);
      final failedState = initialState.failed(testError);

      expect(failedState.localPath, equals(testPath));
      expect(failedState.status, equals(UploadStatus.failed));
      expect(failedState.error, equals(testError));
      expect(failedState.canRetry, isTrue);
    });

    test('should create retrying state and increment retry count', () {
      final initialState = PhotoUploadState.idle(testPath);
      final failedState = initialState.failed(testError);
      final retryingState = failedState.retrying();

      expect(retryingState.localPath, equals(testPath));
      expect(retryingState.status, equals(UploadStatus.retrying));
      expect(retryingState.retryCount, equals(1));
    });

    test('should limit retry attempts to 3', () {
      var state = PhotoUploadState.idle(testPath);
      
      // First attempt fails, can retry (retryCount = 0)
      state = state.failed(testError);
      expect(state.canRetry, isTrue);
      expect(state.retryCount, equals(0));
      
      // First retry (retryCount = 1)
      state = state.retrying();
      expect(state.retryCount, equals(1));
      
      // First retry fails, can still retry (retryCount = 1)
      state = state.failed(testError);
      expect(state.canRetry, isTrue);
      
      // Second retry (retryCount = 2)
      state = state.retrying();
      expect(state.retryCount, equals(2));
      
      // Second retry fails, can still retry (retryCount = 2)
      state = state.failed(testError);
      expect(state.canRetry, isTrue);
      
      // Third retry (retryCount = 3)
      state = state.retrying();
      expect(state.retryCount, equals(3));
      
      // Third retry fails, cannot retry anymore (retryCount = 3)
      state = state.failed(testError);
      expect(state.canRetry, isFalse);
    });

    test('should provide user-friendly error messages', () {
      final networkError = PhotoUploadState.idle(testPath)
          .failed('network connection failed');
      expect(networkError.userFriendlyError, 
          contains('Problème de connexion réseau'));

      final permissionError = PhotoUploadState.idle(testPath)
          .failed('permission denied');
      expect(permissionError.userFriendlyError, 
          contains('Problème d\'autorisation'));

      final sizeError = PhotoUploadState.idle(testPath)
          .failed('file too large');
      expect(sizeError.userFriendlyError, 
          contains('trop volumineux'));

      final genericError = PhotoUploadState.idle(testPath)
          .failed('unknown error');
      expect(genericError.userFriendlyError, 
          contains('Erreur lors de l\'upload'));
    });

    test('should implement equality correctly', () {
      final state1 = PhotoUploadState.idle(testPath);
      final state2 = PhotoUploadState.idle(testPath);
      final state3 = PhotoUploadState.idle('/different/path.jpg');

      expect(state1, equals(state2));
      expect(state1, isNot(equals(state3)));
    });

    test('should have consistent hashCode', () {
      final state1 = PhotoUploadState.idle(testPath);
      final state2 = PhotoUploadState.idle(testPath);

      expect(state1.hashCode, equals(state2.hashCode));
    });

    test('should have proper toString implementation', () {
      final state = PhotoUploadState.idle(testPath);
      final stateString = state.toString();

      expect(stateString, contains('PhotoUploadState'));
      expect(stateString, contains(testPath));
      expect(stateString, contains('idle'));
    });
  });
}