import '../database/database_helper.dart';
import '../models/parcours.dart';

class ParcoursService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Obtenir tous les parcours
  Future<List<Parcours>> obtenirTousParcours() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('parcours');
    return List.generate(maps.length, (i) {
      return Parcours.fromMap(maps[i]);
    });
  }

  // Ajouter un parcours
  Future<int> ajouterParcours(Parcours parcours) async {
    final db = await _databaseHelper.database;
    return await db.insert('parcours', parcours.toMap());
  }

  // Modifier un parcours
  Future<bool> modifierParcours(Parcours parcours) async {
    final db = await _databaseHelper.database;
    final result = await db.update(
      'parcours',
      parcours.toMap(),
      where: 'id = ?',
      whereArgs: [parcours.id],
    );
    return result > 0;
  }

  // Supprimer un parcours
  Future<bool> supprimerParcours(int id) async {
    final db = await _databaseHelper.database;
    final result = await db.delete(
      'parcours',
      where: 'id = ?',
      whereArgs: [id],
    );
    return result > 0;
  }

  // Rechercher des parcours
  Future<List<Parcours>> rechercherParcours(String terme) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'parcours',
      where: 'nom LIKE ? OR description LIKE ?',
      whereArgs: ['%$terme%', '%$terme%'],
    );
    return List.generate(maps.length, (i) {
      return Parcours.fromMap(maps[i]);
    });
  }

  // Obtenir les statistiques
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    final db = await _databaseHelper.database;

    final nombreTotal = await db.rawQuery(
      'SELECT COUNT(*) as count FROM parcours',
    );
    final planifies = await db.rawQuery(
      'SELECT COUNT(*) as count FROM parcours WHERE statut = ?',
      ['planifie'],
    );
    final enCours = await db.rawQuery(
      'SELECT COUNT(*) as count FROM parcours WHERE statut = ?',
      ['en_cours'],
    );
    final termines = await db.rawQuery(
      'SELECT COUNT(*) as count FROM parcours WHERE statut = ?',
      ['termine'],
    );
    final annules = await db.rawQuery(
      'SELECT COUNT(*) as count FROM parcours WHERE statut = ?',
      ['annule'],
    );

    return {
      'nombreTotal': nombreTotal.first['count'],
      'planifies': planifies.first['count'],
      'enCours': enCours.first['count'],
      'termines': termines.first['count'],
      'annules': annules.first['count'],
    };
  }

  // Obtenir un parcours par ID
  Future<Parcours?> obtenirParcoursParId(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'parcours',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Parcours.fromMap(maps.first);
    }
    return null;
  }

  // Obtenir les parcours par merchandiser
  Future<List<Parcours>> obtenirParcoursParMerchandiser(
    int merchandiserId,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'parcours',
      where: 'merchandiserId = ?',
      whereArgs: [merchandiserId],
    );
    return List.generate(maps.length, (i) {
      return Parcours.fromMap(maps[i]);
    });
  }

  // Obtenir les parcours par date
  Future<List<Parcours>> obtenirParcoursParDate(DateTime date) async {
    final db = await _databaseHelper.database;
    final dateStr = date.toIso8601String().substring(0, 10); // YYYY-MM-DD
    final List<Map<String, dynamic>> maps = await db.query(
      'parcours',
      where: 'DATE(dateVisite) = ?',
      whereArgs: [dateStr],
    );
    return List.generate(maps.length, (i) {
      return Parcours.fromMap(maps[i]);
    });
  }

  // Obtenir les parcours par statut
  Future<List<Parcours>> obtenirParcoursParStatut(String statut) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'parcours',
      where: 'statut = ?',
      whereArgs: [statut],
    );
    return List.generate(maps.length, (i) {
      return Parcours.fromMap(maps[i]);
    });
  }

  // Obtenir les parcours d'une période
  Future<List<Parcours>> obtenirParcoursPeriode(
    DateTime debut,
    DateTime fin,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'parcours',
      where: 'dateVisite BETWEEN ? AND ?',
      whereArgs: [debut.toIso8601String(), fin.toIso8601String()],
    );
    return List.generate(maps.length, (i) {
      return Parcours.fromMap(maps[i]);
    });
  }

  // Obtenir les parcours du jour
  Future<List<Parcours>> obtenirParcoursAujourdhui() async {
    final aujourdhui = DateTime.now();
    return await obtenirParcoursParDate(aujourdhui);
  }

  // Obtenir les parcours de la semaine
  Future<List<Parcours>> obtenirParcoursSemaine() async {
    final maintenant = DateTime.now();
    final debutSemaine = maintenant.subtract(
      Duration(days: maintenant.weekday - 1),
    );
    final finSemaine = debutSemaine.add(const Duration(days: 6));
    return await obtenirParcoursPeriode(debutSemaine, finSemaine);
  }
}
