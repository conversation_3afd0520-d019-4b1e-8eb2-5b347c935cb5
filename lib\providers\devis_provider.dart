import 'package:flutter/foundation.dart';
import '../models/devis.dart';
import '../services/devis_service.dart';

class DevisProvider with ChangeNotifier {
  final DevisService _devisService = DevisService();

  List<Devis> _devis = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Devis> get devis => _devis;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Charger tous les devis
  Future<void> chargerDevis() async {
    _isLoading = true;
    notifyListeners();

    try {
      _devis = await _devisService.obtenirTousLesDevis();
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des devis: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Créer un nouveau devis
  Future<bool> creerDevis(Devis devis) async {
    try {
      await _devisService.creerDevis(devis);
      await chargerDevis(); // Recharger la liste
      return true;
    } catch (e) {
      _error = 'Erreur lors de la création du devis: $e';
      notifyListeners();
      return false;
    }
  }

  // Modifier un devis existant
  Future<bool> modifierDevis(Devis devis) async {
    try {
      await _devisService.modifierDevis(devis);
      await chargerDevis(); // Recharger la liste
      return true;
    } catch (e) {
      _error = 'Erreur lors de la modification du devis: $e';
      notifyListeners();
      return false;
    }
  }

  // Générer un numéro de devis unique
  Future<String> genererNumeroDevis() async {
    try {
      return await _devisService.genererNumeroDevis();
    } catch (e) {
      _error = 'Erreur lors de la génération du numéro: $e';
      notifyListeners();
      rethrow;
    }
  }

  // Supprimer un devis
  Future<bool> supprimerDevis(String devisId) async {
    try {
      await _devisService.supprimerDevis(devisId);
      await chargerDevis();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la suppression du devis: $e';
      notifyListeners();
      return false;
    }
  }

  // Dupliquer un devis
  Future<String?> dupliquerDevis(String devisId) async {
    try {
      final nouveauId = await _devisService.dupliquerDevis(devisId);
      await chargerDevis();
      return nouveauId;
    } catch (e) {
      _error = 'Erreur lors de la duplication du devis: $e';
      notifyListeners();
      return null;
    }
  }

  // Transformer un devis en commande
  Future<String?> transformerEnCommande(String devisId) async {
    try {
      final commandeId = await _devisService.transformerEnCommande(devisId);
      await chargerDevis();
      return commandeId;
    } catch (e) {
      _error = 'Erreur lors de la transformation en commande: $e';
      notifyListeners();
      return null;
    }
  }

  // Obtenir un devis par ID
  Devis? obtenirDevisParId(String id) {
    try {
      return _devis.firstWhere((devis) => devis.id == id);
    } catch (e) {
      return null;
    }
  }

  // Obtenir les statistiques des devis
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      return await _devisService.obtenirStatistiques();
    } catch (e) {
      return {'total': 0};
    }
  }

  // Effacer l'erreur
  void effacerErreur() {
    _error = null;
    notifyListeners();
  }
}
