# Configuration Firebase pour VitaBrosse Pro

## 🔧 Étape 1 : Configuration du projet Firebase

### A. <PERSON><PERSON><PERSON> le projet Firebase
1. Allez sur [Firebase Console](https://console.firebase.google.com/)
2. C<PERSON><PERSON> sur "Ajouter un projet"
3. Nom du projet : `vitabrosse-commercial`
4. Activez Google Analytics (recommandé)
5. Choisissez votre pays

### B. Configurer Firestore Database
1. Dans la console Firebase, allez dans "Firestore Database"
2. C<PERSON>z sur "Créer une base de données"
3. Commencez en mode test (règles de sécurité permissives)
4. Choisissez l'emplacement : `europe-west1` (Belgique)

### C. Configurer Authentication
1. Allez dans "Authentication"
2. Cliquez sur "Commencer"
3. Activez les méthodes de connexion :
   - Email/mot de passe
   - Google (optionnel)

## 🔧 Étape 2 : Installation Flutter Firebase

### A. Installer Firebase CLI
```bash
npm install -g firebase-tools
```

### B. Installer FlutterFire CLI
```bash
dart pub global activate flutterfire_cli
```

### C. Connecter Firebase à Flutter
```bash
# Dans le dossier de votre projet
firebase login
flutterfire configure
```

Sélectionnez votre projet `vitabrosse-commercial` et les plateformes (Android, iOS, Web).

## 🔧 Étape 3 : Dépendances Flutter

Ajoutez ces dépendances dans `pubspec.yaml` :

```yaml
dependencies:
  # Firebase
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.6.0
  firebase_analytics: ^10.7.4
  
  # Autres utilitaires
  cached_network_image: ^3.3.0
  image_picker: ^1.0.4
```

## 🔧 Étape 4 : Structure de la base de données

### Collections Firestore

```
vitabrosse_commercial/
├── users/
│   ├── {userId}/
│   │   ├── email: string
│   │   ├── nom: string
│   │   ├── role: string
│   │   ├── createdAt: timestamp
│   │   └── lastLogin: timestamp
│   
├── clients/
│   ├── {clientId}/
│   │   ├── nom: string
│   │   ├── adresse: string
│   │   ├── telephone: string
│   │   ├── email: string
│   │   ├── createdAt: timestamp
│   │   └── updatedAt: timestamp
│   
├── produits/
│   ├── {produitId}/
│   │   ├── nom: string
│   │   ├── description: string
│   │   ├── prix: number
│   │   ├── stock: number
│   │   ├── imageUrl: string
│   │   ├── createdAt: timestamp
│   │   └── updatedAt: timestamp
│   
├── commandes/
│   ├── {commandeId}/
│   │   ├── clientId: string
│   │   ├── items: array
│   │   ├── total: number
│   │   ├── statut: string
│   │   ├── createdAt: timestamp
│   │   └── updatedAt: timestamp
│   
├── devis/
│   ├── {devisId}/
│   │   ├── clientId: string
│   │   ├── items: array
│   │   ├── total: number
│   │   ├── statut: string
│   │   ├── validiteJusquau: timestamp
│   │   ├── createdAt: timestamp
│   │   └── updatedAt: timestamp
│   
├── merchandisers/
│   ├── {merchandiserId}/
│   │   ├── nom: string
│   │   ├── email: string
│   │   ├── telephone: string
│   │   ├── zone: string
│   │   ├── createdAt: timestamp
│   │   └── updatedAt: timestamp
│   
├── magasins/
│   ├── {magasinId}/
│   │   ├── nom: string
│   │   ├── adresse: string
│   │   ├── telephone: string
│   │   ├── merchandiserId: string
│   │   ├── createdAt: timestamp
│   │   └── updatedAt: timestamp
│   
└── visites/
    ├── {visiteId}/
    │   ├── merchandiserId: string
    │   ├── magasinId: string
    │   ├── date: timestamp
    │   ├── rapport: string
    │   ├── photos: array
    │   ├── createdAt: timestamp
    │   └── updatedAt: timestamp
```

## 🔧 Étape 5 : Règles de sécurité Firestore

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Règles pour les utilisateurs
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Règles pour les clients
    match /clients/{clientId} {
      allow read, write: if request.auth != null;
    }
    
    // Règles pour les produits
    match /produits/{produitId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null; // Modifier selon les rôles
    }
    
    // Règles pour les commandes
    match /commandes/{commandeId} {
      allow read, write: if request.auth != null;
    }
    
    // Règles pour les devis
    match /devis/{devisId} {
      allow read, write: if request.auth != null;
    }
    
    // Règles pour les merchandisers
    match /merchandisers/{merchandiserId} {
      allow read, write: if request.auth != null;
    }
    
    // Règles pour les magasins
    match /magasins/{magasinId} {
      allow read, write: if request.auth != null;
    }
    
    // Règles pour les visites
    match /visites/{visiteId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 🔧 Étape 6 : Migration des données

Vous pouvez exporter vos données SQLite actuelles et les importer dans Firestore :

1. Créer un script de migration
2. Exporter les données SQLite en JSON
3. Importer les données dans Firestore via l'Admin SDK

## 🔧 Étape 7 : Configuration des index

Dans la console Firebase, allez dans "Firestore Database" > "Index" et créez ces index composés :

- `clients` : `createdAt` (desc)
- `commandes` : `clientId` (asc), `createdAt` (desc)
- `devis` : `clientId` (asc), `createdAt` (desc)
- `visites` : `merchandiserId` (asc), `date` (desc)

## 🔧 Étape 8 : Backup et sécurité

1. Activez la sauvegarde automatique
2. Configurez les alertes de sécurité
3. Mettez en place le monitoring

## 📱 Avantages de Firebase pour votre application

- **Temps réel** : Synchronisation automatique des données
- **Hors ligne** : Cache local automatique
- **Sécurité** : Règles de sécurité granulaires
- **Scalabilité** : Gestion automatique de la charge
- **Multi-plateforme** : Même base pour Android, iOS et Web
- **Analytics** : Suivi des performances intégré
