class Visite {
  final int? id;
  final int parcoursId;
  final String clientId;
  final DateTime dateVisite;
  final DateTime? heureArrivee;
  final DateTime? heureDepart;
  final String statut; // 'prevue', 'en_cours', 'terminee', 'annulee'
  final String? commentaires;
  final List<String>? photos;
  final Map<String, dynamic>? donneesVisite;
  final DateTime dateCreation;

  Visite({
    this.id,
    required this.parcoursId,
    required this.clientId,
    required this.dateVisite,
    this.heureArrivee,
    this.heureDepart,
    this.statut = 'prevue',
    this.commentaires,
    this.photos,
    this.donneesVisite,
    required this.dateCreation,
  });

  // Convertir une Visite en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'parcoursId': parcoursId,
      'clientId': clientId,
      'dateVisite': dateVisite.toIso8601String(),
      'heureArrivee': heureArrivee?.toIso8601String(),
      'heureDepart': heureDepart?.toIso8601String(),
      'statut': statut,
      'commentaires': commentaires,
      'photos': photos?.join(','),
      'donneesVisite':
          donneesVisite != null
              ? donneesVisite!.entries
                  .map((e) => '${e.key}:${e.value}')
                  .join(';')
              : null,
      'dateCreation': dateCreation.toIso8601String(),
    };
  }

  // Créer une Visite à partir d'une Map de la base de données
  factory Visite.fromMap(Map<String, dynamic> map) {
    Map<String, dynamic>? donneesVisite;
    if (map['donneesVisite'] != null && map['donneesVisite'].isNotEmpty) {
      donneesVisite = {};
      final pairs = map['donneesVisite'].split(';');
      for (String pair in pairs) {
        final keyValue = pair.split(':');
        if (keyValue.length == 2) {
          donneesVisite[keyValue[0]] = keyValue[1];
        }
      }
    }

    return Visite(
      id: map['id'],
      parcoursId: map['parcoursId'],
      clientId: map['clientId'],
      dateVisite: DateTime.parse(map['dateVisite']),
      heureArrivee:
          map['heureArrivee'] != null
              ? DateTime.parse(map['heureArrivee'])
              : null,
      heureDepart:
          map['heureDepart'] != null
              ? DateTime.parse(map['heureDepart'])
              : null,
      statut: map['statut'],
      commentaires: map['commentaires'],
      photos:
          map['photos'] != null && map['photos'].isNotEmpty
              ? map['photos'].split(',')
              : null,
      donneesVisite: donneesVisite,
      dateCreation: DateTime.parse(map['dateCreation']),
    );
  }

  // Créer une copie de la visite avec des modifications
  Visite copyWith({
    int? id,
    int? parcoursId,
    String? clientId,
    DateTime? dateVisite,
    DateTime? heureArrivee,
    DateTime? heureDepart,
    String? statut,
    String? commentaires,
    List<String>? photos,
    Map<String, dynamic>? donneesVisite,
    DateTime? dateCreation,
  }) {
    return Visite(
      id: id ?? this.id,
      parcoursId: parcoursId ?? this.parcoursId,
      clientId: clientId ?? this.clientId,
      dateVisite: dateVisite ?? this.dateVisite,
      heureArrivee: heureArrivee ?? this.heureArrivee,
      heureDepart: heureDepart ?? this.heureDepart,
      statut: statut ?? this.statut,
      commentaires: commentaires ?? this.commentaires,
      photos: photos ?? this.photos,
      donneesVisite: donneesVisite ?? this.donneesVisite,
      dateCreation: dateCreation ?? this.dateCreation,
    );
  }

  // Durée de la visite
  Duration? get duree {
    if (heureArrivee != null && heureDepart != null) {
      return heureDepart!.difference(heureArrivee!);
    }
    return null;
  }

  // Vérifier si la visite est en cours
  bool get estEnCours => statut == 'en_cours';

  // Vérifier si la visite est terminée
  bool get estTerminee => statut == 'terminee';

  @override
  String toString() {
    return 'Visite{id: $id, parcoursId: $parcoursId, clientId: $clientId, statut: $statut}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Visite && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
