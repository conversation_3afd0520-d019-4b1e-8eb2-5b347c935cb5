import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/rapport_merchandising.dart';
import '../../models/mission.dart';
import '../../providers/rapport_provider.dart';
import '../../services/mission_service.dart';

class RapportsMerchandisersScreen extends StatefulWidget {
  const RapportsMerchandisersScreen({super.key});

  @override
  State<RapportsMerchandisersScreen> createState() =>
      _RapportsMerchandisersScreenState();
}

class _RapportsMerchandisersScreenState
    extends State<RapportsMerchandisersScreen> {
  String _filtreStatut = 'tous';
  final MissionService _missionService = MissionService();
  final Map<String, Mission> _missionsCache = {};

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerRapports();
    });
  }

  void _chargerRapports() {
    final provider = Provider.of<RapportProvider>(context, listen: false);
    // Load all merchandising reports for commercial view
    provider.chargerTousLesRapportsMerchandising();
  }

  Future<Mission?> _getMissionInfo(String missionId) async {
    if (_missionsCache.containsKey(missionId)) {
      return _missionsCache[missionId];
    }

    try {
      final mission = await _missionService.obtenirMissionParId(missionId);
      if (mission != null) {
        _missionsCache[missionId] = mission;
      }
      return mission;
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              const Icon(Icons.description, color: Color(0xFF10B981), size: 24),
              const SizedBox(width: 12),
              const Text(
                'Rapports des Merchandisers',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF1E293B),
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(
                  Icons.refresh,
                  color: Color(0xFF1E293B),
                  size: 24,
                ),
                tooltip: 'Actualiser',
                onPressed: _chargerRapports,
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Filter Section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.filter_list,
                  color: Color(0xFF10B981),
                  size: 20,
                ),
                const SizedBox(width: 12),
                const Text(
                  'Filtrer par statut:',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1E293B),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: const Color(0xFFE2E8F0)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButton<String>(
                      value: _filtreStatut,
                      isExpanded: true,
                      underline: const SizedBox(),
                      items: const [
                        DropdownMenuItem(value: 'tous', child: Text('Tous')),
                        DropdownMenuItem(
                          value: 'brouillon',
                          child: Text('Brouillons'),
                        ),
                        DropdownMenuItem(
                          value: 'envoye',
                          child: Text('Envoyés'),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _filtreStatut = value!;
                        });
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Reports List
          Expanded(
            child: Consumer<RapportProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(color: Color(0xFF10B981)),
                  );
                }

                final rapportsFiltres = _filtrerRapports(
                  provider.rapportsMerchandising,
                );

                if (rapportsFiltres.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.description_outlined,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Aucun rapport trouvé',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Les rapports des merchandisers apparaîtront ici',
                          style: TextStyle(color: Colors.grey.shade500),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: rapportsFiltres.length,
                  itemBuilder: (context, index) {
                    final rapport = rapportsFiltres[index];
                    return _buildRapportCard(rapport);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<RapportMerchandising> _filtrerRapports(
    List<RapportMerchandising> rapports,
  ) {
    if (_filtreStatut == 'tous') {
      return rapports;
    }
    return rapports
        .where((rapport) => rapport.statut == _filtreStatut)
        .toList();
  }

  Widget _buildRapportCard(RapportMerchandising rapport) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatutColor(
                      rapport.statut,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    rapport.statut == 'brouillon'
                        ? Icons.edit_note
                        : Icons.send,
                    color: _getStatutTextColor(rapport.statut),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        rapport.titre,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: Color(0xFF1E293B),
                        ),
                      ),
                      const SizedBox(height: 4),
                      FutureBuilder<Mission?>(
                        future: _getMissionInfo(rapport.missionId),
                        builder: (context, snapshot) {
                          if (snapshot.hasData && snapshot.data != null) {
                            final mission = snapshot.data!;
                            return Text(
                              'Mission: ${mission.titre}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Color(0xFF64748B),
                              ),
                            );
                          }
                          return Text(
                            'Mission: ${rapport.missionId}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0xFF64748B),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 2),
                      FutureBuilder<Mission?>(
                        future: _getMissionInfo(rapport.missionId),
                        builder: (context, snapshot) {
                          if (snapshot.hasData && snapshot.data != null) {
                            final mission = snapshot.data!;
                            return Text(
                              'Merchandiser: ${mission.merchandiserId}', // TODO: Get merchandiser name from ID
                              style: const TextStyle(
                                fontSize: 12,
                                color: Color(0xFF64748B),
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Créé le ${rapport.dateCreation.day}/${rapport.dateCreation.month}/${rapport.dateCreation.year}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF64748B),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatutColor(rapport.statut),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatutText(rapport.statut),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: _getStatutTextColor(rapport.statut),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              rapport.observations.length > 100
                  ? '${rapport.observations.substring(0, 100)}...'
                  : rapport.observations,
              style: const TextStyle(fontSize: 14, color: Color(0xFF64748B)),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'envoye':
        return const Color(0xFF10B981).withValues(alpha: 0.1);
      case 'brouillon':
        return const Color(0xFFF59E0B).withValues(alpha: 0.1);
      default:
        return const Color(0xFF64748B).withValues(alpha: 0.1);
    }
  }

  Color _getStatutTextColor(String statut) {
    switch (statut) {
      case 'envoye':
        return const Color(0xFF10B981);
      case 'brouillon':
        return const Color(0xFFF59E0B);
      default:
        return const Color(0xFF64748B);
    }
  }

  String _getStatutText(String statut) {
    switch (statut) {
      case 'envoye':
        return 'Envoyé';
      case 'brouillon':
        return 'Brouillon';
      default:
        return 'Inconnu';
    }
  }
}
