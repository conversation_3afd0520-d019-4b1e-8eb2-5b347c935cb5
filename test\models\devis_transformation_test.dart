import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/devis.dart';
import '../../lib/models/devis_item.dart';
import '../../lib/models/commande.dart';
import '../../lib/models/commande_item.dart';

void main() {
  group('Devis to Commande Transformation Tests', () {
    late Devis sampleDevis;

    setUp(() {
      // Créer un devis de test avec des données complètes
      sampleDevis = Devis(
        id: 'devis_test_123',
        numero: '2024001',
        clientId: 'client_123',
        dateCreation: DateTime(2024, 1, 15),
        dateExpiration: DateTime(2024, 2, 15),
        conditionsValidite: 'Valable 30 jours',
        statut: StatutDevis.accepte,
        items: [
          DevisItem(
            produitId: 'prod_1',
            reference: 'REF001',
            designation: 'Produit Test 1',
            quantite: 2,
            unite: 'pièce',
            prixUnitaireHT: 100.0,
          ),
          DevisItem(
            produitId: 'prod_2',
            reference: 'REF002',
            designation: 'Produit Test 2',
            quantite: 5,
            unite: 'kg',
            prixUnitaireHT: 50.0,
          ),
        ],
        remisePourcentage: 10.0,
        remiseMontant: 0.0,
        tauxTva: 20.0,
        notes: 'Notes du devis test',
        contactCommercial: 'Commercial Test',
      );
    });

    test('should preserve all data when transforming devis to commande', () {
      // Simuler la transformation (logique extraite du service)
      final commandeItems =
          sampleDevis.items
              .map(
                (devisItem) => CommandeItem(
                  produitId: devisItem.produitId,
                  nomProduit: devisItem.designation,
                  codeProduit: devisItem.reference,
                  prixUnitaire: devisItem.prixUnitaireHT,
                  quantite: devisItem.quantite,
                  sousTotal: devisItem.sousTotal,
                  unite: devisItem.unite,
                ),
              )
              .toList();

      final commande = Commande(
        clientId: sampleDevis.clientId,
        dateCommande: DateTime.now(),
        statut: StatutCommande.enAttente,
        montantTotal: sampleDevis.totalTTC,
        notes:
            'Commande créée à partir du devis ${sampleDevis.numeroFormate}${sampleDevis.notes != null ? '\n\nNotes du devis: ${sampleDevis.notes}' : ''}',
        items: commandeItems,
      );

      // Vérifications
      expect(commande.clientId, equals(sampleDevis.clientId));
      expect(commande.statut, equals(StatutCommande.enAttente));
      expect(commande.montantTotal, equals(sampleDevis.totalTTC));
      expect(commande.items.length, equals(sampleDevis.items.length));

      // Vérifier que toutes les notes sont préservées
      expect(commande.notes, contains(sampleDevis.numeroFormate));
      expect(commande.notes, contains(sampleDevis.notes!));

      // Vérifier chaque item
      for (int i = 0; i < sampleDevis.items.length; i++) {
        final devisItem = sampleDevis.items[i];
        final commandeItem = commande.items[i];

        expect(commandeItem.produitId, equals(devisItem.produitId));
        expect(commandeItem.nomProduit, equals(devisItem.designation));
        expect(commandeItem.codeProduit, equals(devisItem.reference));
        expect(commandeItem.prixUnitaire, equals(devisItem.prixUnitaireHT));
        expect(commandeItem.quantite, equals(devisItem.quantite));
        expect(commandeItem.sousTotal, equals(devisItem.sousTotal));
        expect(commandeItem.unite, equals(devisItem.unite));
      }
    });

    test('should correctly calculate totals', () {
      // Vérifier les calculs du devis
      expect(sampleDevis.sousTotal, equals(450.0)); // (2*100) + (5*50)
      expect(sampleDevis.montantRemise, equals(45.0)); // 10% de 450
      expect(sampleDevis.totalHT, equals(405.0)); // 450 - 45
      expect(sampleDevis.montantTva, equals(81.0)); // 20% de 405
      expect(sampleDevis.totalTTC, equals(486.0)); // 405 + 81

      // Simuler la transformation
      final commandeItems =
          sampleDevis.items
              .map(
                (devisItem) => CommandeItem(
                  produitId: devisItem.produitId,
                  nomProduit: devisItem.designation,
                  codeProduit: devisItem.reference,
                  prixUnitaire: devisItem.prixUnitaireHT,
                  quantite: devisItem.quantite,
                  sousTotal: devisItem.sousTotal,
                  unite: devisItem.unite,
                ),
              )
              .toList();

      final commande = Commande(
        clientId: sampleDevis.clientId,
        dateCommande: DateTime.now(),
        statut: StatutCommande.enAttente,
        montantTotal: sampleDevis.totalTTC,
        items: commandeItems,
      );

      // Le montant total de la commande doit correspondre au TTC du devis
      expect(commande.montantTotal, equals(sampleDevis.totalTTC));

      // Vérifier que la somme des sous-totaux des items correspond
      final totalItemsCommande = commande.items.fold<double>(
        0.0,
        (sum, item) => sum + item.sousTotal,
      );
      expect(totalItemsCommande, equals(sampleDevis.sousTotal));
    });

    test('should validate required data before transformation', () {
      // Test avec un devis vide
      final devisVide = sampleDevis.copyWith(items: []);
      expect(devisVide.items.isEmpty, isTrue);

      // Test avec un item ayant des données incomplètes
      final devisIncomplet = sampleDevis.copyWith(
        items: [
          DevisItem(
            produitId: '', // ID vide
            reference: 'REF001',
            designation: 'Produit Test',
            quantite: 0, // Quantité nulle
            unite: 'pièce',
            prixUnitaireHT: 100.0,
          ),
        ],
      );

      // Ces conditions devraient être vérifiées dans le service
      expect(devisIncomplet.items.first.produitId.isEmpty, isTrue);
      expect(devisIncomplet.items.first.quantite <= 0, isTrue);
    });

    test('should only transform accepted devis', () {
      // Test avec différents statuts
      final devisBrouillon = sampleDevis.copyWith(
        statut: StatutDevis.brouillon,
      );
      final devisEnvoye = sampleDevis.copyWith(statut: StatutDevis.envoye);
      final devisRefuse = sampleDevis.copyWith(statut: StatutDevis.refuse);
      final devisExpire = sampleDevis.copyWith(statut: StatutDevis.expire);
      final devisAccepte = sampleDevis.copyWith(statut: StatutDevis.accepte);

      // Seul le devis accepté peut être transformé
      expect(devisBrouillon.peutEtreTransformeEnCommande, isFalse);
      expect(devisEnvoye.peutEtreTransformeEnCommande, isFalse);
      expect(devisRefuse.peutEtreTransformeEnCommande, isFalse);
      expect(devisExpire.peutEtreTransformeEnCommande, isFalse);
      expect(devisAccepte.peutEtreTransformeEnCommande, isTrue);
    });
  });
}
