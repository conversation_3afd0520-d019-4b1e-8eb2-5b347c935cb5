# Design Document

## Overview

This design addresses the Google Drive upload issues in the rapport form by implementing a robust, properly authenticated upload system. The current implementation falls back to simulation mode due to authentication and permission issues. With the service account now properly configured with edit access to the target folder, we can implement a reliable upload mechanism with proper error handling and user feedback.

## Architecture

### Current Issues Analysis
1. **Authentication Flow**: Service account credentials are properly embedded but authentication flow has error handling issues
2. **Folder Access**: Service account now has edit access to folder `1ryAprErbjJ1l-PZSKlOPcMlPxzVZyb9c`
3. **Error Handling**: Current implementation catches all errors and falls back to simulation
4. **User Feedback**: Limited feedback on upload status and errors

### Proposed Architecture
```
[Rapport Form] -> [Enhanced GoogleDriveService] -> [Google Drive API]
       |                      |                          |
   [UI Feedback] <- [Upload Status Manager] <- [Authentication Layer]
```

## Components and Interfaces

### 1. Enhanced GoogleDriveService
**Purpose**: Robust Google Drive upload service with proper error handling

**Key Methods**:
- `uploadImage(File imageFile, String fileName)` - Main upload method
- `testFolderAccess()` - Verify service account permissions
- `deleteImage(String driveUrl)` - Delete uploaded images
- `getUploadStatus(String uploadId)` - Track upload progress

**Authentication Flow**:
1. Create service account credentials from embedded JSON
2. Obtain OAuth 2.0 access token using `googleapis_auth`
3. Test folder access before attempting uploads
4. Use authenticated HTTP client for all API calls

### 2. Upload Status Manager
**Purpose**: Track and manage upload states for UI feedback

**States**:
- `UploadState.idle` - No upload in progress
- `UploadState.uploading` - Upload in progress
- `UploadState.success` - Upload completed successfully
- `UploadState.failed` - Upload failed with error
- `UploadState.retrying` - Retrying failed upload

### 3. Enhanced Rapport Form UI
**Purpose**: Provide clear visual feedback for upload operations

**UI Components**:
- Progress indicators during upload
- Success/error status icons
- Retry buttons for failed uploads
- Clear error messages

## Data Models

### UploadResult
```dart
class UploadResult {
  final bool success;
  final String? driveUrl;
  final String? error;
  final String? fileId;
  
  UploadResult({
    required this.success,
    this.driveUrl,
    this.error,
    this.fileId,
  });
}
```

### PhotoUploadState
```dart
class PhotoUploadState {
  final String localPath;
  final UploadStatus status;
  final String? driveUrl;
  final String? error;
  final double? progress;
  
  PhotoUploadState({
    required this.localPath,
    required this.status,
    this.driveUrl,
    this.error,
    this.progress,
  });
}
```

## Error Handling

### Error Categories
1. **Authentication Errors**
   - Invalid service account credentials
   - Token refresh failures
   - Scope permission issues

2. **Permission Errors**
   - Folder access denied
   - Insufficient permissions for upload
   - File creation restrictions

3. **Network Errors**
   - Connection timeouts
   - Network unavailable
   - API rate limiting

4. **File Errors**
   - File too large
   - Unsupported file format
   - File read errors

### Error Recovery Strategies
1. **Automatic Retry**: For transient network errors (max 3 attempts)
2. **User Retry**: For recoverable errors with user action
3. **Graceful Degradation**: Clear error messages for unrecoverable errors
4. **Logging**: Comprehensive error logging for debugging

## Testing Strategy

### Unit Tests
1. **GoogleDriveService Tests**
   - Authentication flow testing
   - Upload success scenarios
   - Error handling scenarios
   - Folder access verification

2. **Upload Status Manager Tests**
   - State transitions
   - Progress tracking
   - Error state handling

### Integration Tests
1. **End-to-End Upload Flow**
   - Complete photo upload from rapport form
   - Error scenarios with network issues
   - Multiple concurrent uploads

2. **UI Integration Tests**
   - Upload progress display
   - Error message display
   - Retry functionality

### Manual Testing Scenarios
1. **Happy Path**: Upload photos successfully to Google Drive
2. **Network Issues**: Test behavior with poor connectivity
3. **Permission Issues**: Test with restricted folder access
4. **Large Files**: Test upload of large image files
5. **Multiple Uploads**: Test concurrent photo uploads

## Implementation Plan

### Phase 1: Core Service Enhancement
1. Fix authentication flow in GoogleDriveService
2. Implement proper folder access testing
3. Add comprehensive error handling
4. Remove simulation fallback mode

### Phase 2: Upload Status Management
1. Create UploadResult and PhotoUploadState models
2. Implement upload progress tracking
3. Add retry mechanisms for failed uploads

### Phase 3: UI Enhancement
1. Update rapport form with upload status indicators
2. Add progress bars and status icons
3. Implement error message display
4. Add retry buttons for failed uploads

### Phase 4: Testing and Validation
1. Comprehensive testing of all upload scenarios
2. Performance testing with large files
3. Error scenario validation
4. User acceptance testing

## Security Considerations

1. **Credential Management**: Service account credentials remain embedded but are used securely
2. **File Validation**: Validate file types and sizes before upload
3. **Access Control**: Ensure uploaded files maintain appropriate permissions
4. **Error Information**: Avoid exposing sensitive information in error messages

## Performance Considerations

1. **Concurrent Uploads**: Limit concurrent uploads to prevent overwhelming the API
2. **File Size Limits**: Implement reasonable file size limits for mobile uploads
3. **Progress Tracking**: Provide accurate progress feedback for large uploads
4. **Caching**: Cache authentication tokens to reduce API calls

## Monitoring and Logging

1. **Upload Metrics**: Track upload success/failure rates
2. **Error Logging**: Comprehensive error logging with context
3. **Performance Metrics**: Monitor upload times and file sizes
4. **User Feedback**: Log user retry attempts and error recovery