import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/rapport.dart';
import '../models/rapport_merchandising.dart';

class RapportService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'rapports';

  // Créer un nouveau rapport
  Future<void> creerRapport(Rapport rapport) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(rapport.id)
          .set(rapport.toMap());
    } catch (e) {
      throw Exception('Erreur lors de la création du rapport: $e');
    }
  }

  // Obtenir tous les rapports
  Future<List<Rapport>> obtenirTousLesRapports() async {
    try {
      QuerySnapshot snapshot = await _firestore.collection(_collection).get();
      return snapshot.docs
          .map((doc) => Rapport.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Erreur lors de la récupération des rapports: $e');
    }
  }

  // Obtenir les rapports d'un merchandiser
  Future<List<Rapport>> obtenirRapportsParMerchandiser(
    String merchandiserId,
  ) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('merchandiserId', isEqualTo: merchandiserId)
              .orderBy('dateRapport', descending: true)
              .get();

      return snapshot.docs
          .map((doc) => Rapport.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des rapports du merchandiser: $e',
      );
    }
  }

  // Obtenir les rapports pour un commercial
  Future<List<Rapport>> obtenirRapportsParCommercial(
    String commercialId,
  ) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('commercialId', isEqualTo: commercialId)
              .orderBy('dateRapport', descending: true)
              .get();

      return snapshot.docs
          .map((doc) => Rapport.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des rapports du commercial: $e',
      );
    }
  }

  // Obtenir les rapports d'une mission
  Future<List<Rapport>> obtenirRapportsParMission(String missionId) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('missionId', isEqualTo: missionId)
              .orderBy('dateRapport', descending: true)
              .get();

      return snapshot.docs
          .map((doc) => Rapport.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des rapports de la mission: $e',
      );
    }
  }

  // Obtenir un rapport par ID
  Future<Rapport?> obtenirRapportParId(String rapportId) async {
    try {
      DocumentSnapshot doc =
          await _firestore.collection(_collection).doc(rapportId).get();
      if (doc.exists) {
        return Rapport.fromMap(doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      throw Exception('Erreur lors de la récupération du rapport: $e');
    }
  }

  // Mettre à jour le statut d'un rapport
  Future<void> mettreAJourStatut(String rapportId, String nouveauStatut) async {
    try {
      await _firestore.collection(_collection).doc(rapportId).update({
        'statut': nouveauStatut,
      });
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du statut: $e');
    }
  }

  // Mettre à jour un rapport
  Future<void> mettreAJourRapport(Rapport rapport) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(rapport.id)
          .update(rapport.toMap());
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du rapport: $e');
    }
  }

  // Supprimer un rapport
  Future<void> supprimerRapport(String rapportId) async {
    try {
      await _firestore.collection(_collection).doc(rapportId).delete();
    } catch (e) {
      throw Exception('Erreur lors de la suppression du rapport: $e');
    }
  }

  // Obtenir les rapports en brouillon
  Future<List<Rapport>> obtenirRapportsBrouillon(String merchandiserId) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('merchandiserId', isEqualTo: merchandiserId)
              .where('statut', isEqualTo: 'brouillon')
              .orderBy('dateRapport', descending: true)
              .get();

      return snapshot.docs
          .map((doc) => Rapport.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des rapports en brouillon: $e',
      );
    }
  }

  // Obtenir les rapports par statut
  Future<List<Rapport>> obtenirRapportsParStatut(
    String merchandiserId,
    String statut,
  ) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection(_collection)
              .where('merchandiserId', isEqualTo: merchandiserId)
              .where('statut', isEqualTo: statut)
              .orderBy('dateRapport', descending: true)
              .get();

      return snapshot.docs
          .map((doc) => Rapport.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des rapports par statut: $e',
      );
    }
  }

  // Ajouter un feedback du commercial
  Future<void> ajouterFeedbackCommercial(
    String rapportId,
    String feedback,
  ) async {
    try {
      await _firestore.collection(_collection).doc(rapportId).update({
        'feedbackCommercial': feedback,
      });
    } catch (e) {
      throw Exception('Erreur lors de l\'ajout du feedback: $e');
    }
  }

  // Générer un ID unique pour un rapport
  String genererIdRapport() {
    return _firestore.collection(_collection).doc().id;
  }

  // Écouter les changements des rapports d'un merchandiser
  Stream<List<Rapport>> ecouterRapportsParMerchandiser(String merchandiserId) {
    return _firestore
        .collection(_collection)
        .where('merchandiserId', isEqualTo: merchandiserId)
        .orderBy('dateRapport', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map(
                    (doc) =>
                        Rapport.fromMap(doc.data() as Map<String, dynamic>),
                  )
                  .toList(),
        );
  }

  // Écouter les changements des rapports pour un commercial
  Stream<List<Rapport>> ecouterRapportsParCommercial(String commercialId) {
    return _firestore
        .collection(_collection)
        .where('commercialId', isEqualTo: commercialId)
        .orderBy('dateRapport', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map(
                    (doc) =>
                        Rapport.fromMap(doc.data() as Map<String, dynamic>),
                  )
                  .toList(),
        );
  }

  // ===== MERCHANDISING REPORTS METHODS =====

  // Créer un nouveau rapport de merchandising
  Future<void> creerRapportMerchandising(RapportMerchandising rapport) async {
    try {
      // Generate ID if not provided
      final docId =
          rapport.id ??
          _firestore.collection('rapports_merchandising').doc().id;

      await _firestore
          .collection('rapports_merchandising')
          .doc(docId)
          .set(rapport.copyWith(id: docId).toMap());
    } catch (e) {
      throw Exception(
        'Erreur lors de la création du rapport de merchandising: $e',
      );
    }
  }

  // Modifier un rapport de merchandising existant
  Future<void> modifierRapportMerchandising(
    RapportMerchandising rapport,
  ) async {
    try {
      if (rapport.id == null) {
        throw Exception('ID du rapport requis pour la modification');
      }

      await _firestore
          .collection('rapports_merchandising')
          .doc(rapport.id)
          .update(rapport.toMap());
    } catch (e) {
      throw Exception(
        'Erreur lors de la modification du rapport de merchandising: $e',
      );
    }
  }

  // Obtenir tous les rapports de merchandising
  Future<List<RapportMerchandising>>
  obtenirTousLesRapportsMerchandising() async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection('rapports_merchandising')
              .orderBy('dateCreation', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return RapportMerchandising.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des rapports de merchandising: $e',
      );
    }
  }

  // Obtenir les rapports de merchandising d'un merchandiser
  Future<List<RapportMerchandising>>
  obtenirRapportsMerchandisingParMerchandiser(String merchandiserId) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection('rapports_merchandising')
              .where('merchandiserId', isEqualTo: merchandiserId)
              .orderBy('dateCreation', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return RapportMerchandising.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des rapports de merchandising du merchandiser: $e',
      );
    }
  }

  // Obtenir les rapports de merchandising brouillon
  Future<List<RapportMerchandising>> obtenirRapportsMerchandisingBrouillon(
    String merchandiserId,
  ) async {
    try {
      QuerySnapshot snapshot =
          await _firestore
              .collection('rapports_merchandising')
              .where('merchandiserId', isEqualTo: merchandiserId)
              .where('statut', isEqualTo: 'brouillon')
              .orderBy('dateCreation', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return RapportMerchandising.fromMap({...data, 'id': doc.id});
      }).toList();
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des rapports brouillon: $e',
      );
    }
  }

  // Mettre à jour un rapport de merchandising
  Future<void> mettreAJourRapportMerchandising(
    RapportMerchandising rapport,
  ) async {
    try {
      await _firestore
          .collection('rapports_merchandising')
          .doc(rapport.id)
          .update(rapport.toMap());
    } catch (e) {
      throw Exception(
        'Erreur lors de la mise à jour du rapport de merchandising: $e',
      );
    }
  }

  // Supprimer un rapport de merchandising
  Future<void> supprimerRapportMerchandising(String rapportId) async {
    try {
      await _firestore
          .collection('rapports_merchandising')
          .doc(rapportId)
          .delete();
    } catch (e) {
      throw Exception(
        'Erreur lors de la suppression du rapport de merchandising: $e',
      );
    }
  }

  // Obtenir un rapport de merchandising par ID
  Future<RapportMerchandising?> obtenirRapportMerchandisingParId(
    String rapportId,
  ) async {
    try {
      DocumentSnapshot doc =
          await _firestore
              .collection('rapports_merchandising')
              .doc(rapportId)
              .get();

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return RapportMerchandising.fromMap({...data, 'id': doc.id});
      }
      return null;
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération du rapport de merchandising: $e',
      );
    }
  }
}
