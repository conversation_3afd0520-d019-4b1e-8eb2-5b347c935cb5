import 'dart:io';
import 'upload_status.dart';

/// Represents the state of a photo upload operation
class PhotoUploadState {
  final String localPath;
  final UploadStatus status;
  final String? driveUrl;
  final String? error;
  final double? progress;
  final int retryCount;
  final DateTime lastUpdated;

  PhotoUploadState({
    required this.localPath,
    required this.status,
    this.driveUrl,
    this.error,
    this.progress,
    this.retryCount = 0,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  /// Create an initial idle state for a new photo
  factory PhotoUploadState.idle(String localPath) {
    return PhotoUploadState(
      localPath: localPath,
      status: UploadStatus.idle,
    );
  }

  /// Create an uploading state with optional progress
  PhotoUploadState uploading({double? progress}) {
    return PhotoUploadState(
      localPath: localPath,
      status: UploadStatus.uploading,
      progress: progress,
      retryCount: retryCount,
    );
  }

  /// Create a success state with the Google Drive URL
  PhotoUploadState success(String driveUrl) {
    return PhotoUploadState(
      localPath: localPath,
      status: UploadStatus.success,
      driveUrl: driveUrl,
      retryCount: retryCount,
    );
  }

  /// Create a failed state with error message
  PhotoUploadState failed(String error) {
    return PhotoUploadState(
      localPath: localPath,
      status: UploadStatus.failed,
      error: error,
      retryCount: retryCount,
    );
  }

  /// Create a retrying state (increments retry count)
  PhotoUploadState retrying() {
    return PhotoUploadState(
      localPath: localPath,
      status: UploadStatus.retrying,
      retryCount: retryCount + 1,
    );
  }

  /// Get the local file for this photo
  File get file => File(localPath);

  /// Check if this photo has been successfully uploaded
  bool get isUploaded => status == UploadStatus.success && driveUrl != null;

  /// Check if this photo can be retried
  bool get canRetry => status.canRetry && retryCount < 3;

  /// Get a user-friendly error message
  String get userFriendlyError {
    if (error == null) return '';
    
    // Map technical errors to user-friendly messages
    if (error!.contains('network') || error!.contains('connection')) {
      return 'Problème de connexion réseau. Vérifiez votre connexion internet.';
    } else if (error!.contains('permission') || error!.contains('access')) {
      return 'Problème d\'autorisation. Contactez l\'administrateur.';
    } else if (error!.contains('size') || error!.contains('large')) {
      return 'Le fichier est trop volumineux pour être uploadé.';
    } else if (error!.contains('format') || error!.contains('type')) {
      return 'Format de fichier non supporté.';
    } else {
      return 'Erreur lors de l\'upload. Réessayez plus tard.';
    }
  }

  @override
  String toString() {
    return 'PhotoUploadState(localPath: $localPath, status: $status, '
        'driveUrl: $driveUrl, error: $error, progress: $progress, '
        'retryCount: $retryCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PhotoUploadState &&
        other.localPath == localPath &&
        other.status == status &&
        other.driveUrl == driveUrl &&
        other.error == error &&
        other.progress == progress &&
        other.retryCount == retryCount;
  }

  @override
  int get hashCode {
    return localPath.hashCode ^
        status.hashCode ^
        driveUrl.hashCode ^
        error.hashCode ^
        progress.hashCode ^
        retryCount.hashCode;
  }
}