import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../providers/tache_merchandising_provider.dart';
import '../../providers/mission_provider.dart';
import '../../models/mission.dart';
import 'missions_date_screen.dart';

class CalendrierMerchandisingScreen extends StatefulWidget {
  const CalendrierMerchandisingScreen({super.key});

  @override
  State<CalendrierMerchandisingScreen> createState() =>
      _CalendrierMerchandisingScreenState();
}

class _CalendrierMerchandisingScreenState
    extends State<CalendrierMerchandisingScreen> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  CalendarFormat _calendarFormat = CalendarFormat.month;

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerTaches();
    });
  }

  void _chargerTaches() {
    final tacheProvider = Provider.of<TacheMerchandisingProvider>(
      context,
      listen: false,
    );
    final missionProvider = Provider.of<MissionProvider>(
      context,
      listen: false,
    );
    tacheProvider.chargerTaches();
    missionProvider.chargerMissions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calendrier Merchandising'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _chargerTaches,
          ),
        ],
      ),
      body: Consumer2<TacheMerchandisingProvider, MissionProvider>(
        builder: (context, tacheProvider, missionProvider, child) {
          return Column(
            children: [
              // Calendrier
              Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TableCalendar<Mission>(
                  firstDay: DateTime.utc(2020, 1, 1),
                  lastDay: DateTime.utc(2030, 12, 31),
                  focusedDay: _focusedDay,
                  calendarFormat: _calendarFormat,
                  eventLoader: _getMissionsForDay,
                  startingDayOfWeek: StartingDayOfWeek.monday,
                  selectedDayPredicate: (day) {
                    return isSameDay(_selectedDay, day);
                  },
                  onDaySelected: (selectedDay, focusedDay) {
                    if (!isSameDay(_selectedDay, selectedDay)) {
                      setState(() {
                        _selectedDay = selectedDay;
                        _focusedDay = focusedDay;
                      });
                      // Navigate to missions screen for the selected date
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) =>
                                  MissionsDateScreen(selectedDate: selectedDay),
                        ),
                      );
                    }
                  },
                  onFormatChanged: (format) {
                    if (_calendarFormat != format) {
                      setState(() {
                        _calendarFormat = format;
                      });
                    }
                  },
                  onPageChanged: (focusedDay) {
                    _focusedDay = focusedDay;
                  },
                  calendarStyle: const CalendarStyle(
                    outsideDaysVisible: false,
                    weekendTextStyle: TextStyle(color: Colors.red),
                    holidayTextStyle: TextStyle(color: Colors.red),
                  ),
                  headerStyle: const HeaderStyle(
                    formatButtonVisible: true,
                    titleCentered: true,
                    formatButtonShowsNext: false,
                    formatButtonDecoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.all(Radius.circular(12.0)),
                    ),
                    formatButtonTextStyle: TextStyle(color: Colors.white),
                  ),
                ),
              ),

              // Information sur les missions du jour sélectionné
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Text(
                          _selectedDay == null
                              ? 'Sélectionnez une date pour voir les missions'
                              : 'Missions du ${_selectedDay!.day}/${_selectedDay!.month}/${_selectedDay!.year}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Expanded(child: _buildMissionsList(missionProvider)),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  List<Mission> _getMissionsForDay(DateTime day) {
    final provider = Provider.of<MissionProvider>(context, listen: false);
    return provider.missions.where((mission) {
      return isSameDay(mission.dateEcheance, day);
    }).toList();
  }

  Widget _buildMissionsList(MissionProvider provider) {
    if (provider.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_selectedDay == null) {
      return const Center(
        child: Text('Sélectionnez une date pour voir les missions'),
      );
    }

    final missionsDuJour = _getMissionsForDay(_selectedDay!);

    if (missionsDuJour.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_available, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            const Text(
              'Aucune mission pour cette date',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            MissionsDateScreen(selectedDate: _selectedDay!),
                  ),
                );
              },
              child: const Text('Voir les détails'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            itemCount: missionsDuJour.length,
            itemBuilder: (context, index) {
              final mission = missionsDuJour[index];
              return _buildMissionCard(mission);
            },
          ),
        ),
        const SizedBox(height: 8),
        TextButton.icon(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) =>
                        MissionsDateScreen(selectedDate: _selectedDay!),
              ),
            );
          },
          icon: const Icon(Icons.arrow_forward),
          label: Text('Voir toutes les missions (${missionsDuJour.length})'),
          style: TextButton.styleFrom(foregroundColor: const Color(0xFF10B981)),
        ),
      ],
    );
  }

  Widget _buildMissionCard(Mission mission) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 4,
          height: double.infinity,
          color: _getPrioriteColor(mission.priorite),
        ),
        title: Text(
          mission.titre,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(mission.description),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.person_outline, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  mission.clientNom,
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatutColorMission(
                      mission.statut,
                    ).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatutTextMission(mission.statut),
                    style: TextStyle(
                      color: _getStatutColorMission(mission.statut),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) =>
                      MissionsDateScreen(selectedDate: mission.dateEcheance),
            ),
          );
        },
      ),
    );
  }

  // Helper methods for mission colors and text
  Color _getPrioriteColor(String priorite) {
    switch (priorite) {
      case 'urgente':
        return Colors.red;
      case 'haute':
        return Colors.orange;
      case 'normale':
        return Colors.blue;
      case 'faible':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

  Color _getStatutColorMission(String statut) {
    switch (statut) {
      case 'terminee':
        return Colors.green;
      case 'en_cours':
        return Colors.orange;
      case 'en_attente':
        return Colors.blue;
      case 'annulee':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  String _getStatutTextMission(String statut) {
    switch (statut) {
      case 'terminee':
        return 'Terminée';
      case 'en_cours':
        return 'En cours';
      case 'en_attente':
        return 'En attente';
      case 'annulee':
        return 'Annulée';
      default:
        return 'En attente';
    }
  }
}
